package cn.edu.xmut.tsa.student.module.business.education.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 教育情况新增表单
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Schema(description = "教育情况新增表单")
public class StudentEducationAddForm {

    @Schema(description = "就读学校名称")
    @NotBlank(message = "就读学校名称不能为空")
    private String schoolName;

    @Schema(description = "国家（地区）")
    @NotBlank(message = "国家（地区）不能为空")
    private String country;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalDate startDate;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private LocalDate endDate;

    @Schema(description = "受教育程度")
    @NotBlank(message = "受教育程度不能为空")
    private String educationLevel;
}
