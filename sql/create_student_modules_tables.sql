-- 创建教育情况表
CREATE TABLE `t_student_education` (
  `education_id` bigint NOT NULL AUTO_INCREMENT COMMENT '教育情况ID',
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `school_name` varchar(200) NOT NULL COMMENT '就读学校名称',
  `country` varchar(100) NOT NULL COMMENT '国家（地区）',
  `start_date` date NOT NULL COMMENT '开始时间',
  `end_date` date NOT NULL COMMENT '结束时间',
  `education_level` varchar(100) NOT NULL COMMENT '受教育程度',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`education_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_deleted_flag` (`deleted_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生教育情况表';

-- 创建专业志愿表
CREATE TABLE `t_student_major_choice` (
  `choice_id` bigint NOT NULL AUTO_INCREMENT COMMENT '专业志愿ID',
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `subject_type` int NOT NULL COMMENT '科类',
  `first_major_id` bigint NOT NULL COMMENT '第一专业志愿ID',
  `second_major_id` bigint NOT NULL COMMENT '第二专业志愿ID',
  `third_major_id` bigint NOT NULL COMMENT '第三专业志愿ID',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`choice_id`),
  UNIQUE KEY `uk_student_id` (`student_id`, `deleted_flag`),
  KEY `idx_subject_type` (`subject_type`),
  KEY `idx_deleted_flag` (`deleted_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生专业志愿表';

-- 创建学生资料表
CREATE TABLE `t_student_document` (
  `document_id` bigint NOT NULL AUTO_INCREMENT COMMENT '资料ID',
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `document_type` int NOT NULL COMMENT '资料类型：1-报名表扫描件，2-台湾居民身份证明扫描件，3-台胞证扫描件，4-高中毕业证书扫描件，5-成绩单扫描件，6-获奖证书和其他相关证明材料',
  `file_name` varchar(500) NOT NULL COMMENT '文件名称',
  `file_path` varchar(1000) NOT NULL COMMENT '文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件类型',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`document_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_document_type` (`document_type`),
  KEY `idx_deleted_flag` (`deleted_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生资料表';
