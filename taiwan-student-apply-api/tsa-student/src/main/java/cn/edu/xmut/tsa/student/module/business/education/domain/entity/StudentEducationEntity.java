package cn.edu.xmut.tsa.student.module.business.education.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 教育情况实体类
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_student_education")
public class StudentEducationEntity {

    /**
     * 教育情况ID
     */
    @TableId(type = IdType.AUTO)
    private Long educationId;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 就读学校名称
     */
    private String schoolName;

    /**
     * 国家（地区）
     */
    private String country;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 受教育程度
     */
    private String educationLevel;

    /**
     * 是否删除：0-否，1-是
     */
    private Boolean deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
