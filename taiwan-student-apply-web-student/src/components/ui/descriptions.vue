<script setup>
  import { cn } from '/@/lib/utils.js';

  const props = defineProps({
    column: {
      type: Number,
      default: 3,
    },
    bordered: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'default',
    },
    class: String,
  });

  const getSizeClasses = () => {
    const sizeMap = {
      small: 'text-sm',
      default: 'text-base',
      large: 'text-lg',
    };
    return sizeMap[props.size] || sizeMap.default;
  };
</script>

<template>
  <div :class="cn('w-full', getSizeClasses(), props.class)">
    <div v-if="bordered" class="border rounded-lg overflow-hidden">
      <div class="divide-y">
        <slot />
      </div>
    </div>
    <div v-else class="space-y-2">
      <slot />
    </div>
  </div>
</template>
