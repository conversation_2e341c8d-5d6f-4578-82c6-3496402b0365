package cn.edu.xmut.tsa.base.common.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考生状态枚举
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Getter
@AllArgsConstructor
public enum StudentStatusEnum implements BaseEnum {

    /**
     * 资料待完善
     */
    PENDING_INFO(0, "资料待完善"),

    /**
     * 资料审核中
     */
    UNDER_REVIEW(1, "资料审核中"),

    /**
     * 审核通过
     */
    APPROVED(2, "审核通过");

    private final Integer value;
    private final String desc;

    public static StudentStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (StudentStatusEnum statusEnum : values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}
