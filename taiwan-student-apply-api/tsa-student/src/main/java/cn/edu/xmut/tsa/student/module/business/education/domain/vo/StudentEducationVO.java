package cn.edu.xmut.tsa.student.module.business.education.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 教育情况VO
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Schema(description = "教育情况VO")
public class StudentEducationVO {

    @Schema(description = "教育情况ID")
    private Long educationId;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "就读学校名称")
    private String schoolName;

    @Schema(description = "国家（地区）")
    private String country;

    @Schema(description = "开始时间")
    private LocalDate startDate;

    @Schema(description = "结束时间")
    private LocalDate endDate;

    @Schema(description = "受教育程度")
    private String educationLevel;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
