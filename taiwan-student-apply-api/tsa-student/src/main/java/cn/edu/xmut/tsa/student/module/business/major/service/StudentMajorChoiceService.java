package cn.edu.xmut.tsa.student.module.business.major.service;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.base.common.util.SmartEnumUtil;
import cn.edu.xmut.tsa.student.module.business.major.constant.SubjectTypeEnum;
import cn.edu.xmut.tsa.student.module.business.major.dao.StudentMajorChoiceDao;
import cn.edu.xmut.tsa.student.module.business.major.domain.entity.StudentMajorChoiceEntity;
import cn.edu.xmut.tsa.student.module.business.major.domain.form.StudentMajorChoiceForm;
import cn.edu.xmut.tsa.student.module.business.major.domain.vo.EnrollmentPlanStudentVO;
import cn.edu.xmut.tsa.student.module.business.major.domain.vo.StudentMajorChoiceVO;
import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEvent;
import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEventType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 专业志愿服务
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Service
public class StudentMajorChoiceService {

    @Resource
    private StudentMajorChoiceDao studentMajorChoiceDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 查询学生专业志愿
     *
     * @param studentId 学生ID
     * @return 专业志愿
     */
    public ResponseDTO<StudentMajorChoiceVO> getByStudentId(Long studentId) {
        try {
            StudentMajorChoiceVO vo = studentMajorChoiceDao.getByStudentId(studentId);
            if (vo != null) {
                // 设置科类名称
                vo.setSubjectTypeName(SmartEnumUtil.getEnumDescByValue(vo.getSubjectType(), SubjectTypeEnum.class));
            }
            return ResponseDTO.ok(vo);
        } catch (Exception e) {
            log.error("查询专业志愿失败", e);
            return ResponseDTO.userErrorParam("查询失败，请稍后重试");
        }
    }

    /**
     * 保存专业志愿
     *
     * @param studentId 学生ID
     * @param form      专业志愿表单
     * @return 保存结果
     */
    public ResponseDTO<String> saveMajorChoice(Long studentId, StudentMajorChoiceForm form) {
        try {
            // 校验科类是否有效
            if (!SmartEnumUtil.checkEnum(form.getSubjectType(), SubjectTypeEnum.class)) {
                return ResponseDTO.userErrorParam("科类参数无效");
            }

            // 校验三个专业志愿不能相同
            Set<Long> majorIds = new HashSet<>();
            majorIds.add(form.getFirstMajorId());
            majorIds.add(form.getSecondMajorId());
            majorIds.add(form.getThirdMajorId());
            if (majorIds.size() != 3) {
                return ResponseDTO.userErrorParam("三个专业志愿不能相同");
            }

            // 校验招生计划是否有效且属于指定科类
            Integer currentYear = LocalDateTime.now().getYear();
            List<EnrollmentPlanStudentVO> plans = studentMajorChoiceDao.queryEnrollmentPlanBySubjectType(form.getSubjectType(), currentYear);
            Set<Long> validPlanIds = new HashSet<>();
            for (EnrollmentPlanStudentVO plan : plans) {
                validPlanIds.add(plan.getEnrollmentPlanId());
            }

            if (!validPlanIds.contains(form.getFirstMajorId())) {
                return ResponseDTO.userErrorParam("第一专业志愿无效或不属于选择的科类");
            }
            if (!validPlanIds.contains(form.getSecondMajorId())) {
                return ResponseDTO.userErrorParam("第二专业志愿无效或不属于选择的科类");
            }
            if (!validPlanIds.contains(form.getThirdMajorId())) {
                return ResponseDTO.userErrorParam("第三专业志愿无效或不属于选择的科类");
            }

            // 查询是否已存在记录
            StudentMajorChoiceEntity existEntity = null;
            List<StudentMajorChoiceEntity> existingList = studentMajorChoiceDao.selectList(null);
            for (StudentMajorChoiceEntity entity : existingList) {
                if (entity.getStudentId().equals(studentId) && !entity.getDeletedFlag()) {
                    existEntity = entity;
                    break;
                }
            }

            if (existEntity != null) {
                // 更新
                existEntity.setSubjectType(form.getSubjectType());
                existEntity.setFirstMajorId(form.getFirstMajorId());
                existEntity.setSecondMajorId(form.getSecondMajorId());
                existEntity.setThirdMajorId(form.getThirdMajorId());
                existEntity.setUpdateTime(LocalDateTime.now());
                studentMajorChoiceDao.updateById(existEntity);
            } else {
                // 新增
                StudentMajorChoiceEntity entity = SmartBeanUtil.copy(form, StudentMajorChoiceEntity.class);
                entity.setStudentId(studentId);
                entity.setDeletedFlag(false);
                entity.setCreateTime(LocalDateTime.now());
                entity.setUpdateTime(LocalDateTime.now());
                studentMajorChoiceDao.insert(entity);
            }

            // 发布学生状态变更事件
            publishStatusChangeEvent(studentId, StudentStatusChangeEventType.MAJOR_CHOICE_CHANGED);

            return ResponseDTO.ok("专业志愿保存成功");
        } catch (Exception e) {
            log.error("保存专业志愿失败", e);
            return ResponseDTO.userErrorParam("保存失败，请稍后重试");
        }
    }

    /**
     * 根据科类查询当年招生计划
     *
     * @param subjectType 科类
     * @return 招生计划列表
     */
    public ResponseDTO<List<EnrollmentPlanStudentVO>> queryEnrollmentPlanBySubjectType(Integer subjectType) {
        try {
            // 校验科类是否有效
            if (!SmartEnumUtil.checkEnum(subjectType, SubjectTypeEnum.class)) {
                return ResponseDTO.userErrorParam("科类参数无效");
            }

            Integer currentYear = LocalDateTime.now().getYear();
            List<EnrollmentPlanStudentVO> plans = studentMajorChoiceDao.queryEnrollmentPlanBySubjectType(subjectType, currentYear);

            // 设置科类名称
            for (EnrollmentPlanStudentVO plan : plans) {
                plan.setSubjectTypeName(SmartEnumUtil.getEnumDescByValue(plan.getSubjectType(), SubjectTypeEnum.class));
            }

            return ResponseDTO.ok(plans);
        } catch (Exception e) {
            log.error("查询招生计划失败", e);
            return ResponseDTO.userErrorParam("查询失败，请稍后重试");
        }
    }

    /**
     * 检查学生是否已选择专业志愿
     *
     * @param studentId 学生ID
     * @return 是否已选择
     */
    public boolean hasMajorChoice(Long studentId) {
        Long count = studentMajorChoiceDao.countByStudentId(studentId);
        return count > 0;
    }

    /**
     * 发布学生状态变更事件
     *
     * @param studentId 学生ID
     * @param eventType 事件类型
     */
    private void publishStatusChangeEvent(Long studentId, StudentStatusChangeEventType eventType) {
        try {
            StudentStatusChangeEvent event = new StudentStatusChangeEvent(this, studentId, eventType);
            eventPublisher.publishEvent(event);
            log.debug("发布学生状态变更事件成功, studentId: {}, eventType: {}", studentId, eventType.getDesc());
        } catch (Exception e) {
            log.error("发布学生状态变更事件失败, studentId: {}, eventType: {}", studentId, eventType.getDesc(), e);
        }
    }
}
