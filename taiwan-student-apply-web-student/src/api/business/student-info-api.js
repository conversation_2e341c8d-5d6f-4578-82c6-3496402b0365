/*
 * 学生个人信息API
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-22
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

import { postRequest, getRequest } from '/@/lib/axios';

export const studentInfoApi = {
  /**
   * @description: 获取学生个人信息 <AUTHOR>
   * @return {Promise} 学生个人信息
   */
  getStudentInfo: () => {
    return getRequest('/student/info');
  },

  /**
   * @description: 更新学生个人信息 <AUTHOR>
   * @param {Object} studentInfo 学生个人信息
   * @return {Promise} 更新结果
   */
  updateStudentInfo: (studentInfo) => {
    return postRequest('/student/info/update', studentInfo);
  },

  /**
   * @description: 获取学生状态 <AUTHOR>
   * @return {Promise} 学生状态信息
   */
  getStudentStatus: () => {
    return getRequest('/student/status');
  },

  /**
   * @description: 提交申请 <AUTHOR>
   * @return {Promise} 提交结果
   */
  submitApplication: () => {
    return postRequest('/student/submit');
  },
};
