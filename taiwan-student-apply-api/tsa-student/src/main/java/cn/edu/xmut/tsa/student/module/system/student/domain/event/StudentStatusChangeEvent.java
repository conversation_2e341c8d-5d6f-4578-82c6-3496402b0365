package cn.edu.xmut.tsa.student.module.system.student.domain.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 学生状态变更事件
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Getter
public class StudentStatusChangeEvent extends ApplicationEvent {

    /**
     * 学生ID
     */
    private final Long studentId;

    /**
     * 事件类型
     */
    private final StudentStatusChangeEventType eventType;

    public StudentStatusChangeEvent(Object source, Long studentId, StudentStatusChangeEventType eventType) {
        super(source);
        this.studentId = studentId;
        this.eventType = eventType;
    }
}
