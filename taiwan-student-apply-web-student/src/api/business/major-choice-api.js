/*
 * 专业志愿管理API
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-21 11:00:00
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { getRequest, postRequest } from '/@/lib/axios';

export const majorChoiceApi = {
  /**
   * 查询学生专业志愿信息 <AUTHOR>
   */
  queryMajorChoice: () => {
    return getRequest('/student/major-choice/get');
  },

  /**
   * 保存/更新学生专业志愿 <AUTHOR>
   */
  saveMajorChoice: (form) => {
    return postRequest('/student/major-choice/save', form);
  },

  /**
   * 根据科类查询招生计划列表 <AUTHOR>
   */
  queryEnrollmentPlanBySubjectType: (subjectType) => {
    return getRequest(`/student/major-choice/enrollment-plan/list/${subjectType}`);
  },
};
