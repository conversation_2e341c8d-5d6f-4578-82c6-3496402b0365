package cn.edu.xmut.tsa.base.module.support.file.example;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileDownloadVO;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileUploadVO;
import cn.edu.xmut.tsa.base.module.support.file.service.IFileStorageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 亿方云文件上传使用示例
 * <p>
 * 演示如何在Controller中使用亿方云文件存储服务
 *
 * <AUTHOR>
 * @Date 2025-06-21
 */
@Slf4j
@RestController
@RequestMapping("/api/fangcloud/file")
public class FangcloudFileExampleController {

    @Resource
    private IFileStorageService fileStorageService;

    /**
     * 上传文件到亿方云
     *
     * @param file 上传的文件
     * @param path 存储路径
     * @return 上传结果
     */
    @PostMapping("/upload")
    public ResponseDTO<FileUploadVO> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "path", defaultValue = "upload/") String path) {

        log.info("开始上传文件到亿方云，文件名: {}, 大小: {}, 路径: {}",
                file.getOriginalFilename(), file.getSize(), path);

        try {
            ResponseDTO<FileUploadVO> result = fileStorageService.upload(file, path);

            if (result.getOk()) {
                log.info("文件上传成功，文件ID: {}, 访问链接: {}",
                        result.getData().getFileKey(), result.getData().getFileUrl());
            } else {
                log.error("文件上传失败: {}", result.getMsg());
            }

            return result;

        } catch (Exception e) {
            log.error("文件上传过程中发生异常", e);
            return ResponseDTO.userErrorParam("文件上传失败");
        }
    }

    /**
     * 获取文件访问链接
     *
     * @param fileKey 文件ID
     * @return 文件访问链接
     */
    @GetMapping("/url/{fileKey}")
    public ResponseDTO<String> getFileUrl(@PathVariable String fileKey) {
        log.info("获取文件访问链接，文件ID: {}", fileKey);

        try {
            ResponseDTO<String> result = fileStorageService.getFileUrl(fileKey);

            if (result.getOk()) {
                log.info("获取文件链接成功: {}", result.getData());
            } else {
                log.error("获取文件链接失败: {}", result.getMsg());
            }

            return result;

        } catch (Exception e) {
            log.error("获取文件链接过程中发生异常", e);
            return ResponseDTO.userErrorParam("获取文件链接失败");
        }
    }

    /**
     * 下载文件
     *
     * @param fileKey 文件ID
     * @return 文件内容
     */
    @GetMapping("/download/{fileKey}")
    public ResponseEntity<byte[]> downloadFile(@PathVariable String fileKey) {
        log.info("开始下载文件，文件ID: {}", fileKey);

        try {
            ResponseDTO<FileDownloadVO> result = fileStorageService.download(fileKey);

            if (!result.getOk()) {
                log.error("文件下载失败: {}", result.getMsg());
                return ResponseEntity.notFound().build();
            }

            FileDownloadVO downloadVO = result.getData();
            byte[] fileData = downloadVO.getData();

            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            // 设置文件名
            if (downloadVO.getMetadata() != null && downloadVO.getMetadata().getFileName() != null) {
                String fileName = URLEncoder.encode(downloadVO.getMetadata().getFileName(), StandardCharsets.UTF_8);
                headers.setContentDispositionFormData("attachment", fileName);
            }

            headers.setContentLength(fileData.length);

            log.info("文件下载成功，大小: {} 字节", fileData.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileData);

        } catch (Exception e) {
            log.error("文件下载过程中发生异常", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除文件
     *
     * @param fileKey 文件ID
     * @return 删除结果
     */
    @DeleteMapping("/{fileKey}")
    public ResponseDTO<String> deleteFile(@PathVariable String fileKey) {
        log.info("开始删除文件，文件ID: {}", fileKey);

        try {
            ResponseDTO<String> result = fileStorageService.delete(fileKey);

            if (result.getOk()) {
                log.info("文件删除成功，文件ID: {}", fileKey);
            } else {
                log.error("文件删除失败: {}", result.getMsg());
            }

            return result;

        } catch (Exception e) {
            log.error("文件删除过程中发生异常", e);
            return ResponseDTO.userErrorParam("文件删除失败");
        }
    }

    /**
     * 批量上传文件示例
     *
     * @param files 多个文件
     * @param path  存储路径
     * @return 上传结果列表
     */
    @PostMapping("/batch-upload")
    public ResponseDTO<?> batchUploadFiles(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "path", defaultValue = "batch/") String path) {

        log.info("开始批量上传文件，文件数量: {}, 路径: {}", files.length, path);

        try {
            java.util.List<FileUploadVO> successList = new java.util.ArrayList<>();
            java.util.List<String> failList = new java.util.ArrayList<>();

            for (MultipartFile file : files) {
                try {
                    ResponseDTO<FileUploadVO> result = fileStorageService.upload(file, path);
                    if (result.getOk()) {
                        successList.add(result.getData());
                        log.info("文件 {} 上传成功", file.getOriginalFilename());
                    } else {
                        failList.add(file.getOriginalFilename() + ": " + result.getMsg());
                        log.error("文件 {} 上传失败: {}", file.getOriginalFilename(), result.getMsg());
                    }
                } catch (Exception e) {
                    failList.add(file.getOriginalFilename() + ": " + e.getMessage());
                    log.error("文件 {} 上传异常", file.getOriginalFilename(), e);
                }
            }

            java.util.Map<String, Object> resultMap = new java.util.HashMap<>();
            resultMap.put("success", successList);
            resultMap.put("failed", failList);
            resultMap.put("successCount", successList.size());
            resultMap.put("failedCount", failList.size());

            log.info("批量上传完成，成功: {} 个，失败: {} 个", successList.size(), failList.size());

            return ResponseDTO.ok(resultMap);

        } catch (Exception e) {
            log.error("批量上传过程中发生异常", e);
            return ResponseDTO.userErrorParam("批量上传失败");
        }
    }
}
