<template>
  <div class="student-info-complete">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-page-header title="个人信息完善" sub-title="请完善您的个人信息和教育经历">
        <template #extra>
          <a-tag :color="getStatusColor(studentStatus)">
            {{ getStatusDesc(studentStatus) }}
          </a-tag>
        </template>
      </a-page-header>
    </div>

    <!-- 个人信息表单 -->
    <a-card title="基本信息" :bordered="false" style="margin-bottom: 24px">
      <a-form
        ref="personalFormRef"
        :model="personalForm"
        :rules="personalRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="handlePersonalSubmit"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="姓名" name="realName">
              <a-input v-model:value="personalForm.realName" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别" name="gender">
              <a-select v-model:value="personalForm.gender" placeholder="请选择性别">
                <a-select-option :value="1">男</a-select-option>
                <a-select-option :value="2">女</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="出生日期" name="birthday">
              <a-date-picker v-model:value="personalForm.birthday" placeholder="请选择出生日期" style="width: 100%" value-format="YYYY-MM-DD" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="身份证号" name="idCard">
              <a-input v-model:value="personalForm.idCard" placeholder="请输入身份证号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="手机号码" name="phone">
              <a-input v-model:value="personalForm.phone" placeholder="请输入手机号码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="电子邮箱" name="email">
              <a-input v-model:value="personalForm.email" placeholder="请输入电子邮箱" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="通信地址" name="address">
              <a-input v-model:value="personalForm.address" placeholder="请输入通信地址" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="personalSubmitLoading"> 保存基本信息 </a-button>
            <a-button @click="handlePersonalReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 教育经历 -->
    <a-card title="教育经历" :bordered="false">
      <template #extra>
        <a-button type="primary" @click="showEducationModal">
          <template #icon>
            <PlusOutlined />
          </template>
          添加教育经历
        </a-button>
      </template>

      <!-- 教育经历列表 -->
      <a-table :columns="educationColumns" :data-source="educationList" :loading="educationLoading" row-key="educationId" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'startDate'">
            {{ $dayjs(record.startDate).format('YYYY-MM-DD') }}
          </template>
          <template v-if="column.key === 'endDate'">
            {{ $dayjs(record.endDate).format('YYYY-MM-DD') }}
          </template>
          <template v-if="column.key === 'educationLevel'">
            {{ getEducationLevelDesc(record.educationLevel) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEducationModal(record)"> 编辑 </a-button>
              <a-popconfirm title="确定要删除这条教育经历吗？" @confirm="handleEducationDelete(record.educationId)">
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 教育经历弹窗 -->
    <a-modal
      v-model:open="educationModalVisible"
      :title="educationIsEdit ? '编辑教育经历' : '添加教育经历'"
      @ok="handleEducationSubmit"
      @cancel="handleEducationCancel"
      :confirm-loading="educationSubmitLoading"
      width="600px"
    >
      <a-form ref="educationFormRef" :model="educationForm" :rules="educationRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="就读学校名称" name="schoolName">
          <a-input v-model:value="educationForm.schoolName" placeholder="请输入就读学校名称" />
        </a-form-item>

        <a-form-item label="国家（地区）" name="country">
          <a-input v-model:value="educationForm.country" placeholder="请输入国家或地区" />
        </a-form-item>

        <a-form-item label="开始时间" name="startDate">
          <a-date-picker v-model:value="educationForm.startDate" placeholder="请选择开始时间" style="width: 100%" value-format="YYYY-MM-DD" />
        </a-form-item>

        <a-form-item label="结束时间" name="endDate">
          <a-date-picker v-model:value="educationForm.endDate" placeholder="请选择结束时间" style="width: 100%" value-format="YYYY-MM-DD" />
        </a-form-item>

        <a-form-item label="受教育程度" name="educationLevel">
          <a-select v-model:value="educationForm.educationLevel" placeholder="请选择受教育程度">
            <a-select-option v-for="(item, key) in smartEnums.EDUCATION_LEVEL_ENUM" :key="key" :value="item.value">
              {{ item.desc }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { studentInfoApi } from '/@/api/business/student-info-api';
  import { educationApi } from '/@/api/business/education-api';
  import { useStudentApplicationStore } from '/@/store/modules/student-application';
  import smartEnums from '/@/constants';

  // ------------------------------ 响应式数据 ------------------------------

  const studentApplicationStore = useStudentApplicationStore();

  // 学生状态 - 从store获取
  const studentStatus = computed(() => studentApplicationStore.studentStatus);

  // ------------------------------ 个人信息相关 ------------------------------

  // 个人信息表单
  const personalForm = reactive({
    realName: '',
    gender: undefined,
    birthday: undefined,
    idCard: '',
    phone: '',
    email: '',
    address: '',
  });

  // 个人信息表单校验规则
  const personalRules = {
    realName: [{ required: true, message: '请输入姓名' }],
    gender: [{ required: true, message: '请选择性别' }],
    birthday: [{ required: true, message: '请选择出生日期' }],
    idCard: [{ required: true, message: '请输入身份证号' }],
    phone: [{ required: true, message: '请输入手机号码' }],
    email: [{ required: true, type: 'email', message: '请输入正确的电子邮箱' }],
    address: [{ required: true, message: '请输入通信地址' }],
  };

  // 个人信息提交加载状态
  const personalSubmitLoading = ref(false);

  // 个人信息表单引用
  const personalFormRef = ref();

  // ------------------------------ 教育经历相关 ------------------------------

  // 教育经历列表
  const educationList = ref([]);
  const educationLoading = ref(false);

  // 教育经历表格列定义
  const educationColumns = [
    {
      title: '就读学校名称',
      dataIndex: 'schoolName',
      key: 'schoolName',
    },
    {
      title: '国家（地区）',
      dataIndex: 'country',
      key: 'country',
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      key: 'endDate',
    },
    {
      title: '受教育程度',
      dataIndex: 'educationLevel',
      key: 'educationLevel',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];

  // 教育经历弹窗相关
  const educationModalVisible = ref(false);
  const educationIsEdit = ref(false);
  const educationSubmitLoading = ref(false);

  // 教育经历表单
  const educationForm = reactive({
    educationId: undefined,
    schoolName: '',
    country: '',
    startDate: undefined,
    endDate: undefined,
    educationLevel: undefined,
  });

  // 教育经历表单默认值
  const educationFormDefault = {
    educationId: undefined,
    schoolName: '',
    country: '',
    startDate: undefined,
    endDate: undefined,
    educationLevel: undefined,
  };

  // 教育经历表单校验规则
  const educationRules = {
    schoolName: [{ required: true, message: '请输入就读学校名称' }],
    country: [{ required: true, message: '请输入国家或地区' }],
    startDate: [{ required: true, message: '请选择开始时间' }],
    endDate: [{ required: true, message: '请选择结束时间' }],
    educationLevel: [{ required: true, message: '请选择受教育程度' }],
  };

  // 教育经历表单引用
  const educationFormRef = ref();

  // ------------------------------ 个人信息方法 ------------------------------

  /**
   * 获取学生个人信息
   */
  const queryStudentInfo = async () => {
    try {
      const res = await studentInfoApi.getStudentInfo();
      if (res.data) {
        Object.assign(personalForm, res.data);
      }
    } catch (error) {
      console.error('获取学生信息失败:', error);
    }
  };

  /**
   * 保存个人信息
   */
  const handlePersonalSubmit = async () => {
    personalSubmitLoading.value = true;
    try {
      await studentInfoApi.updateStudentInfo(personalForm);
      message.success('个人信息保存成功');
      // 刷新个人信息完成状态
      await studentApplicationStore.checkPersonalInfoStatus();
    } catch (error) {
      console.error('保存个人信息失败:', error);
      message.error('保存失败，请重试');
    } finally {
      personalSubmitLoading.value = false;
    }
  };

  /**
   * 重置个人信息表单
   */
  const handlePersonalReset = () => {
    personalFormRef.value.resetFields();
  };

  // ------------------------------ 教育经历方法 ------------------------------

  /**
   * 获取教育经历列表
   */
  const queryEducationList = async () => {
    educationLoading.value = true;
    try {
      const res = await educationApi.queryEducationList();
      educationList.value = res.data || [];
    } catch (error) {
      console.error('获取教育经历失败:', error);
      message.error('获取教育经历失败');
    } finally {
      educationLoading.value = false;
    }
  };

  /**
   * 显示教育经历弹窗
   */
  const showEducationModal = (record) => {
    if (record) {
      // 编辑模式
      educationIsEdit.value = true;
      Object.assign(educationForm, record);
    } else {
      // 新增模式
      educationIsEdit.value = false;
      Object.assign(educationForm, educationFormDefault);
    }
    educationModalVisible.value = true;
  };

  /**
   * 取消教育经历弹窗
   */
  const handleEducationCancel = () => {
    educationModalVisible.value = false;
    Object.assign(educationForm, educationFormDefault);
    educationFormRef.value?.resetFields();
  };

  /**
   * 提交教育经历
   */
  const handleEducationSubmit = async () => {
    try {
      await educationFormRef.value.validate();
      educationSubmitLoading.value = true;

      if (educationIsEdit.value) {
        await educationApi.updateEducation(educationForm);
        message.success('教育经历更新成功');
      } else {
        await educationApi.addEducation(educationForm);
        message.success('教育经历添加成功');
      }

      handleEducationCancel();
      queryEducationList();
      // 刷新教育经历完成状态
      await studentApplicationStore.checkEducationStatus();
    } catch (error) {
      if (error.errorFields) {
        message.error('请检查表单填写');
        return;
      }
      console.error('保存教育经历失败:', error);
      message.error('保存失败，请重试');
    } finally {
      educationSubmitLoading.value = false;
    }
  };

  /**
   * 删除教育经历
   */
  const handleEducationDelete = async (educationId) => {
    try {
      await educationApi.deleteEducation(educationId);
      message.success('删除成功');
      queryEducationList();
      // 刷新教育经历完成状态
      await studentApplicationStore.checkEducationStatus();
    } catch (error) {
      console.error('删除教育经历失败:', error);
      message.error('删除失败，请重试');
    }
  };

  // ------------------------------ 工具方法 ------------------------------

  /**
   * 获取教育程度描述
   */
  const getEducationLevelDesc = (level) => {
    const levelEnum = Object.values(smartEnums.EDUCATION_LEVEL_ENUM).find((item) => item.value === level);
    return levelEnum ? levelEnum.desc : '-';
  };

  /**
   * 获取学生状态描述
   */
  const getStatusDesc = (status) => {
    const statusEnum = Object.values(smartEnums.STUDENT_STATUS_ENUM || {}).find((item) => item.value === status);
    return statusEnum ? statusEnum.desc : '未知状态';
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status) => {
    const colorMap = {
      0: 'default',
      1: 'processing',
      2: 'success',
      3: 'warning',
      4: 'error',
    };
    return colorMap[status] || 'default';
  };

  /**
   * 获取学生状态
   */
  const queryStudentStatus = async () => {
    await studentApplicationStore.getStudentStatus();
  };

  // ------------------------------ 生命周期 ------------------------------

  onMounted(() => {
    queryStudentInfo();
    queryEducationList();
    queryStudentStatus();
  });
</script>

<style scoped>
  .student-info-complete {
    padding: 0 16px 16px 16px;
  }

  .page-header {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .ant-form-item {
    margin-bottom: 16px;
  }
</style>
