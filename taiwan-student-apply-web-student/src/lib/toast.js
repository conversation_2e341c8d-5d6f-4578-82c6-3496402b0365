import { ref, createApp } from 'vue';
import Toast from '/@/components/ui/toast.vue';

const toasts = ref([]);

let toastId = 0;

export function useToast() {
  const addToast = (options) => {
    const id = ++toastId;
    const toast = {
      id,
      ...options,
    };

    toasts.value.push(toast);

    // 自动移除
    if (options.duration !== 0) {
      setTimeout(() => {
        removeToast(id);
      }, options.duration || 3000);
    }

    return id;
  };

  const removeToast = (id) => {
    const index = toasts.value.findIndex((toast) => toast.id === id);
    if (index > -1) {
      toasts.value.splice(index, 1);
    }
  };

  const success = (message, options = {}) => {
    return addToast({
      type: 'success',
      title: '成功',
      description: message,
      ...options,
    });
  };

  const error = (message, options = {}) => {
    return addToast({
      type: 'error',
      title: '错误',
      description: message,
      ...options,
    });
  };

  const warning = (message, options = {}) => {
    return addToast({
      type: 'warning',
      title: '警告',
      description: message,
      ...options,
    });
  };

  const info = (message, options = {}) => {
    return addToast({
      type: 'default',
      title: '提示',
      description: message,
      ...options,
    });
  };

  return {
    toasts,
    addToast,
    removeToast,
    success,
    error,
    warning,
    info,
  };
}

// 全局 toast 实例
export const toast = useToast();
