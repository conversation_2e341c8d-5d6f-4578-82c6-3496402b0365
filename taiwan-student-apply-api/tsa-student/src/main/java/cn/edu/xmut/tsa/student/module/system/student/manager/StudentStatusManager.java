package cn.edu.xmut.tsa.student.module.system.student.manager;

import cn.edu.xmut.tsa.base.common.enumeration.StudentStatusEnum;
import cn.edu.xmut.tsa.student.module.business.document.dao.StudentDocumentDao;
import cn.edu.xmut.tsa.student.module.business.education.dao.StudentEducationDao;
import cn.edu.xmut.tsa.student.module.business.major.dao.StudentMajorChoiceDao;
import cn.edu.xmut.tsa.student.module.system.student.dao.StudentDao;
import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import cn.edu.xmut.tsa.student.module.business.document.constant.DocumentTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 学生状态管理器
 * 统一处理学生状态检查和更新逻辑
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Service
public class StudentStatusManager {

    @Resource
    private StudentDao studentDao;

    @Resource
    private StudentEducationDao studentEducationDao;

    @Resource
    private StudentMajorChoiceDao studentMajorChoiceDao;

    @Resource
    private StudentDocumentDao studentDocumentDao;

    /**
     * 检查并更新学生状态
     *
     * @param studentId 学生ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkAndUpdateStudentStatus(Long studentId) {
        try {
            StudentEntity studentEntity = studentDao.selectById(studentId);
            if (studentEntity == null || studentEntity.getDeletedFlag()) {
                log.warn("学生不存在或已删除, studentId: {}", studentId);
                return;
            }

            // 如果当前状态是审核通过，则不再更新
            if (StudentStatusEnum.APPROVED.getValue().equals(studentEntity.getCurrentStatus())) {
                log.debug("学生状态已审核通过，无需更新, studentId: {}", studentId);
                return;
            }

            // 检查个人信息是否完整
            if (isPersonalInfoComplete(studentEntity)) {
                // 检查是否满足状态更新为审核中的条件
                updateToUnderReviewIfMeetConditions(studentId, studentEntity);
            } else {
                // 个人信息不完整，设置为资料待完善
                updateToPendingInfo(studentEntity, "请完善个人基础信息");
            }

            studentEntity.setUpdateTime(LocalDateTime.now());
            studentDao.updateById(studentEntity);

            log.info("学生状态检查完成, studentId: {}, 当前状态: {}", 
                studentId, StudentStatusEnum.getByValue(studentEntity.getCurrentStatus()));

        } catch (Exception e) {
            log.error("检查学生状态更新失败, studentId: {}", studentId, e);
            throw e;
        }
    }

    /**
     * 检查个人信息是否完整
     *
     * @param studentEntity 学生实体
     * @return 是否完整
     */
    private boolean isPersonalInfoComplete(StudentEntity studentEntity) {
        return studentEntity.getAvatar() != null &&
               studentEntity.getGender() != null &&
               studentEntity.getPhone() != null &&
               studentEntity.getEmail() != null &&
               studentEntity.getTaiwanPassportNumber() != null &&
               studentEntity.getTaiwanIdCardNumber() != null &&
               studentEntity.getBirthPlace() != null &&
               studentEntity.getBirthDate() != null &&
               studentEntity.getMailingAddress() != null &&
               studentEntity.getContactPhone() != null &&
               studentEntity.getEmergencyContactName() != null &&
               studentEntity.getEmergencyContactPhone() != null &&
               studentEntity.getPersonalStatement() != null;
    }

    /**
     * 检查是否满足审核中条件并更新状态
     * 满足条件：1.个人信息全部完善 2.已有教育情况 3.已选择专业志愿 4.必须上传的资料全部上传
     *
     * @param studentId 学生ID
     * @param studentEntity 学生实体
     */
    private void updateToUnderReviewIfMeetConditions(Long studentId, StudentEntity studentEntity) {
        // 1. 个人信息已完善（在调用此方法前已检查）
        
        // 2. 检查是否有教育情况
        boolean hasEducation = hasEducationInfo(studentId);
        
        // 3. 检查是否已选择专业志愿
        boolean hasMajorChoice = hasMajorChoiceInfo(studentId);
        
        // 4. 检查是否已上传所有必需资料
        boolean hasAllRequiredDocuments = hasAllRequiredDocuments(studentId);
        
        if (hasEducation && hasMajorChoice && hasAllRequiredDocuments) {
            // 满足所有条件，更新为审核中
            studentEntity.setCurrentStatus(StudentStatusEnum.UNDER_REVIEW.getValue());
            studentEntity.setPendingInfo(null);
            log.info("学生满足审核条件，状态更新为审核中, studentId: {}", studentId);
        } else {
            // 不满足条件，保持资料待完善状态，更新提示信息
            StringBuilder pendingInfo = new StringBuilder("请完善以下信息：");
            if (!hasEducation) {
                pendingInfo.append("教育经历、");
            }
            if (!hasMajorChoice) {
                pendingInfo.append("专业志愿、");
            }
            if (!hasAllRequiredDocuments) {
                pendingInfo.append("必需资料上传、");
            }
            // 删除最后的顿号
            if (pendingInfo.toString().endsWith("、")) {
                pendingInfo.setLength(pendingInfo.length() - 1);
            }
            updateToPendingInfo(studentEntity, pendingInfo.toString());
        }
    }

    /**
     * 更新为资料待完善状态
     *
     * @param studentEntity 学生实体
     * @param pendingInfo 待完善信息
     */
    private void updateToPendingInfo(StudentEntity studentEntity, String pendingInfo) {
        studentEntity.setCurrentStatus(StudentStatusEnum.PENDING_INFO.getValue());
        studentEntity.setPendingInfo(pendingInfo);
    }

    /**
     * 检查学生是否有教育情况
     *
     * @param studentId 学生ID
     * @return 是否有教育情况
     */
    public boolean hasEducationInfo(Long studentId) {
        Long count = studentEducationDao.countByStudentId(studentId);
        return count > 0;
    }

    /**
     * 检查学生是否已选择专业志愿
     *
     * @param studentId 学生ID
     * @return 是否已选择
     */
    public boolean hasMajorChoiceInfo(Long studentId) {
        Long count = studentMajorChoiceDao.countByStudentId(studentId);
        return count > 0;
    }

    /**
     * 检查学生是否已上传所有必需资料
     *
     * @param studentId 学生ID
     * @return 是否已上传所有必需资料
     */
    public boolean hasAllRequiredDocuments(Long studentId) {
        Long count = studentDocumentDao.countRequiredDocumentsByStudentId(studentId);
        return count >= DocumentTypeEnum.getRequiredTypes().length;
    }
}
