package cn.edu.xmut.tsa.student.module.system.student.domain.event;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学生状态变更事件类型
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Getter
@AllArgsConstructor
public enum StudentStatusChangeEventType {

    /**
     * 个人信息更新
     */
    PERSONAL_INFO_UPDATED("个人信息更新"),

    /**
     * 教育情况变更
     */
    EDUCATION_INFO_CHANGED("教育情况变更"),

    /**
     * 专业志愿变更
     */
    MAJOR_CHOICE_CHANGED("专业志愿变更"),

    /**
     * 资料上传变更
     */
    DOCUMENT_CHANGED("资料上传变更");

    private final String desc;
}
