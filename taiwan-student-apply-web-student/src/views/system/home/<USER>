<template>
  <div class="home-container">
    <a-row :gutter="24">
      <!-- 个人信息卡片 -->
      <a-col :xs="24" :lg="16">
        <a-card>
          <template #title>
            <a-space>
              <user-outlined />
              <span>个人信息</span>
            </a-space>
          </template>

          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="姓名">
              {{ userInfo.actualName || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="邮箱">
              {{ userInfo.email || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="手机号">
              {{ userInfo.phone || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="准考证号">
              {{ userInfo.examNumber || '暂未分配' }}
            </a-descriptions-item>
            <a-descriptions-item label="当前状态">
              <a-tag :color="getStatusColor(userInfo.currentStatus)">
                {{ getStatusText(userInfo.currentStatus) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="注册时间">
              {{ formatDateTime(userInfo.createTime) }}
            </a-descriptions-item>
          </a-descriptions>

          <div v-if="userInfo.pendingInfo" style="margin-top: 16px">
            <a-alert :message="userInfo.pendingInfo" type="warning" show-icon />
          </div>

          <div style="margin-top: 24px; text-align: right">
            <a-space>
              <a-button v-if="userInfo.currentStatus === 0" type="primary" @click="goToStudentInfo" size="large">
                <template #icon>
                  <edit-outlined />
                </template>
                完善个人信息
              </a-button>
              <a-button v-else @click="goToStudentInfo" size="large">
                <template #icon>
                  <eye-outlined />
                </template>
                查看个人信息
              </a-button>
            </a-space>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧指引 -->
      <a-col :xs="24" :lg="8">
        <a-space direction="vertical" size="large" style="width: 100%">
          <!-- 申请流程 -->
          <a-card title="申请流程" size="small">
            <a-steps direction="vertical" size="small" :current="getCurrentStep()">
              <a-step title="完善个人信息" description="填写详细的个人信息和证件资料">
                <template #icon>
                  <user-outlined />
                </template>
              </a-step>
              <a-step title="提交申请材料" description="上传相关证明文件和材料">
                <template #icon>
                  <file-text-outlined />
                </template>
              </a-step>
              <a-step title="等待审核" description="等待院校审核申请材料">
                <template #icon>
                  <clock-circle-outlined />
                </template>
              </a-step>
              <a-step title="查看结果" description="查看申请审核结果">
                <template #icon>
                  <check-circle-outlined />
                </template>
              </a-step>
            </a-steps>
          </a-card>

          <!-- 申请状态说明 -->
          <a-card title="状态说明" size="small">
            <a-space direction="vertical" size="middle" style="width: 100%">
              <div class="status-item">
                <a-tag color="orange">资料待完善</a-tag>
                <a-typography-text type="secondary" style="margin-left: 8px"> 请完善个人资料信息 </a-typography-text>
              </div>
              <div class="status-item">
                <a-tag color="blue">资料审核中</a-tag>
                <a-typography-text type="secondary" style="margin-left: 8px"> 资料已提交，正在审核中 </a-typography-text>
              </div>
              <div class="status-item">
                <a-tag color="green">审核通过</a-tag>
                <a-typography-text type="secondary" style="margin-left: 8px"> 恭喜！您的申请已通过审核 </a-typography-text>
              </div>
            </a-space>
          </a-card>

          <!-- 帮助信息 -->
          <a-card title="帮助信息" size="small">
            <a-typography-paragraph>
              <a-typography-text strong>注意事项：</a-typography-text>
            </a-typography-paragraph>
            <ul style="padding-left: 20px; margin: 0">
              <li>请确保填写信息的真实性和准确性</li>
              <li>上传的证件照片需要清晰可见</li>
              <li>联系方式请保持畅通，以便及时接收通知</li>
              <li>如有疑问，请联系招生办公室</li>
            </ul>
          </a-card>
        </a-space>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { toast } from '/@/lib/toast';
  import { User, Edit, Eye, Clock } from 'lucide-vue-next';
  import { studentApi } from '/@/api/system/student-api';
  import { SmartLoading } from '/@/components/framework/smart-loading';

  // 组件选项
  defineOptions({
    name: 'StudentHome',
  });

  // 路由
  const router = useRouter();

  // ------------------------------ 用户信息相关 ------------------------------

  // 用户信息
  const userInfo = ref({});

  // 获取学生信息
  async function getStudentInfo() {
    try {
      SmartLoading.show();
      const res = await studentApi.getStudentInfo();
      userInfo.value = res.data;
    } catch (error) {
      toast.error('获取用户信息失败');
    } finally {
      SmartLoading.hide();
    }
  }

  // 获取状态颜色
  function getStatusColor(status) {
    switch (status) {
      case 0:
        return 'orange';
      case 1:
        return 'blue';
      case 2:
        return 'green';
      default:
        return 'default';
    }
  }

  // 获取状态文本
  function getStatusText(status) {
    switch (status) {
      case 0:
        return '资料待完善';
      case 1:
        return '资料审核中';
      case 2:
        return '审核通过';
      default:
        return '未知状态';
    }
  }

  // 获取当前步骤
  function getCurrentStep() {
    switch (userInfo.value.currentStatus) {
      case 0:
        return 0; // 完善个人信息
      case 1:
        return 1; // 提交申请材料
      case 2:
        return 3; // 查看结果
      default:
        return 0;
    }
  }

  // 格式化日期时间
  function formatDateTime(dateTime) {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleString('zh-CN');
  }

  // ------------------------------ 页面跳转 ------------------------------

  // 跳转到学生信息页面
  function goToStudentInfo() {
    router.push('/student-info');
  }

  // ------------------------------ 生命周期 ------------------------------

  onMounted(() => {
    getStudentInfo();
  });
</script>

<style scoped>
  .home-container {
    padding: 24px;
  }

  .status-item {
    display: flex;
    align-items: center;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .home-container {
      padding: 16px;
    }
  }
</style>
