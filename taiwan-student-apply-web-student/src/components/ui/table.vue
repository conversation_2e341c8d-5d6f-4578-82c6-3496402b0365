<script setup>
  import { cn } from '/@/lib/utils.js';

  const props = defineProps({
    columns: Array,
    dataSource: Array,
    loading: <PERSON><PERSON><PERSON>,
    rowKey: String,
    class: String,
  });

  const emit = defineEmits(['row-click']);

  const handleRowClick = (record, index) => {
    emit('row-click', record, index);
  };
</script>

<template>
  <div :class="cn('w-full', props.class)">
    <div class="relative w-full overflow-auto">
      <table class="w-full caption-bottom text-sm">
        <thead class="[&_tr]:border-b">
          <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
            <th
              v-for="column in columns"
              :key="column.key || column.dataIndex"
              class="h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]"
              :style="{ width: column.width ? column.width + 'px' : 'auto' }"
            >
              {{ column.title }}
            </th>
          </tr>
        </thead>
        <tbody class="[&_tr:last-child]:border-0">
          <tr v-if="loading" class="border-b transition-colors hover:bg-muted/50">
            <td :colspan="columns.length" class="p-2 align-middle text-center">
              <div class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span class="ml-2">加载中...</span>
              </div>
            </td>
          </tr>
          <tr v-else-if="!dataSource || dataSource.length === 0" class="border-b transition-colors hover:bg-muted/50">
            <td :colspan="columns.length" class="p-2 align-middle text-center text-muted-foreground">暂无数据</td>
          </tr>
          <tr
            v-else
            v-for="(record, index) in dataSource"
            :key="record[rowKey] || index"
            class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted cursor-pointer"
            @click="handleRowClick(record, index)"
          >
            <td
              v-for="column in columns"
              :key="column.key || column.dataIndex"
              class="p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]"
            >
              <slot :name="column.key" :record="record" :column="column" :index="index">
                {{ record[column.dataIndex] }}
              </slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
