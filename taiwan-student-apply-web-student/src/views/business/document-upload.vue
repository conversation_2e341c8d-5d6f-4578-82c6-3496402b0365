<template>
  <div class="document-upload">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-page-header title="文件上传" sub-title="请按要求上传相关证明文件" />
    </div>

    <a-card title="资料上传" :bordered="false">
      <div class="upload-sections">
        <!-- 循环显示各种资料类型 -->
        <div v-for="(docType, key) in smartEnums.DOCUMENT_TYPE_ENUM" :key="key" class="upload-section">
          <div class="section-header">
            <h4>
              {{ docType.desc }}
              <a-tag v-if="docType.required" color="red">必传</a-tag>
              <a-tag v-else color="blue">选传</a-tag>
            </h4>
          </div>

          <div class="upload-content">
            <!-- 已上传的文件 -->
            <div v-if="getUploadedFile(docType.value)" class="uploaded-file">
              <a-card size="small">
                <template #actions>
                  <a-tooltip title="下载">
                    <DownloadOutlined @click="handleDownload(getUploadedFile(docType.value))" />
                  </a-tooltip>
                  <a-tooltip title="删除">
                    <DeleteOutlined @click="handleDelete(getUploadedFile(docType.value))" />
                  </a-tooltip>
                </template>

                <a-card-meta>
                  <template #title>
                    <div class="file-info">
                      <FileTextOutlined style="margin-right: 8px; color: #1890ff" />
                      {{ getUploadedFile(docType.value).fileName }}
                    </div>
                  </template>
                  <template #description>
                    <div class="file-details">
                      <p>文件大小: {{ formatFileSize(getUploadedFile(docType.value).fileSize) }}</p>
                      <p>上传时间: {{ $dayjs(getUploadedFile(docType.value).createTime).format('YYYY-MM-DD HH:mm:ss') }}</p>
                    </div>
                  </template>
                </a-card-meta>
              </a-card>
            </div>

            <!-- 上传组件 -->
            <div v-else class="upload-area">
              <a-upload-dragger
                :multiple="false"
                :before-upload="(file) => handleBeforeUpload(file, docType.value)"
                :show-upload-list="false"
                accept=".pdf,.jpg,.jpeg,.png,.zip,.rar"
              >
                <p class="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p class="ant-upload-hint">
                  支持格式：PDF、JPG、PNG、ZIP、RAR
                  <br />
                  文件大小不超过 {{ getFileSizeLimit(docType.value) }}
                </p>
              </a-upload-dragger>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploadProgress.visible" class="upload-progress">
        <a-card size="small" title="文件上传中...">
          <a-progress :percent="uploadProgress.percent" :status="uploadProgress.status" />
          <p>{{ uploadProgress.fileName }}</p>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { toast } from '/@/lib/toast';
  import { InboxOutlined, DownloadOutlined, DeleteOutlined, FileTextOutlined } from '@ant-design/icons-vue';
  import { documentApi } from '/@/api/business/document-api';
  import { useStudentApplicationStore } from '/@/store/modules/student-application';
  import smartEnums from '/@/constants';

  // 响应式数据
  const studentApplicationStore = useStudentApplicationStore();
  const documentList = ref([]);
  const loading = ref(false);

  // 上传进度
  const uploadProgress = reactive({
    visible: false,
    percent: 0,
    status: 'active',
    fileName: '',
  });

  // 获取已上传的文件
  const getUploadedFile = (documentType) => {
    return documentList.value.find((doc) => doc.documentType === documentType);
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件大小限制描述
  const getFileSizeLimit = (documentType) => {
    return formatFileSize(smartEnums.FILE_SIZE_LIMIT.DOCUMENT);
  };

  // 验证文件类型
  const validateFileType = (file) => {
    const fileName = file.name.toLowerCase();
    const supportedTypes = smartEnums.UPLOAD_FILE_TYPES.DOCUMENT;
    const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);

    if (!supportedTypes.includes(fileExtension)) {
      toast.error(`不支持的文件格式，请上传：${supportedTypes.join('、')} 格式的文件`);
      return false;
    }
    return true;
  };

  // 验证文件大小
  const validateFileSize = (file) => {
    const maxSize = smartEnums.FILE_SIZE_LIMIT.DOCUMENT;
    if (file.size > maxSize) {
      toast.error(`文件大小不能超过 ${formatFileSize(maxSize)}`);
      return false;
    }
    return true;
  };

  // 上传前验证
  const handleBeforeUpload = async (file, documentType) => {
    // 验证文件类型和大小
    if (!validateFileType(file) || !validateFileSize(file)) {
      return false;
    }

    // 开始上传
    await handleUpload(file, documentType);
    return false; // 阻止默认上传行为
  };

  // 处理文件上传
  const handleUpload = async (file, documentType) => {
    try {
      // 显示上传进度
      uploadProgress.visible = true;
      uploadProgress.percent = 0;
      uploadProgress.status = 'active';
      uploadProgress.fileName = file.name;

      // 构建FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('documentType', documentType);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        if (uploadProgress.percent < 90) {
          uploadProgress.percent += 10;
        }
      }, 100);

      // 上传文件
      const res = await documentApi.uploadDocument(formData);

      clearInterval(progressInterval);
      uploadProgress.percent = 100;
      uploadProgress.status = 'success';

      toast.success('文件上传成功');

      // 刷新文件列表
      await queryDocumentList();
      // 刷新文件上传完成状态
      await studentApplicationStore.checkDocumentUploadStatus();

      // 隐藏进度条
      setTimeout(() => {
        uploadProgress.visible = false;
      }, 1000);
    } catch (error) {
      uploadProgress.status = 'exception';
      toast.error('文件上传失败');

      setTimeout(() => {
        uploadProgress.visible = false;
      }, 2000);
    }
  };

  // 下载文件
  const handleDownload = async (document) => {
    try {
      const res = await documentApi.downloadDocument(document.documentId);

      // 创建下载链接
      const blob = new Blob([res]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = document.fileName;
      link.click();

      // 清理
      window.URL.revokeObjectURL(url);
      toast.success('文件下载成功');
    } catch (error) {
      toast.error('文件下载失败');
    }
  };

  // 删除文件
  const handleDelete = async (document) => {
    try {
      await documentApi.deleteDocument(document.documentId);
      toast.success('文件删除成功');
      await queryDocumentList();
      // 刷新文件上传完成状态
      await studentApplicationStore.checkDocumentUploadStatus();
    } catch (error) {
      toast.error('文件删除失败');
    }
  };

  // 查询文档列表
  const queryDocumentList = async () => {
    try {
      loading.value = true;
      const res = await documentApi.queryDocumentList();
      if (res.data) {
        documentList.value = res.data;
      }
    } catch (error) {
      toast.error('查询资料列表失败');
    } finally {
      loading.value = false;
    }
  };

  // 组件挂载时查询数据
  onMounted(() => {
    queryDocumentList();
  });
</script>

<style scoped>
  .document-upload {
    padding: 0 16px 16px 16px;
  }

  .page-header {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .upload-sections {
    display: grid;
    gap: 24px;
  }

  .upload-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 16px;
    background-color: #fafafa;
  }

  .section-header h4 {
    margin-bottom: 16px;
    color: #1890ff;
  }

  .uploaded-file {
    max-width: 400px;
  }

  .file-info {
    display: flex;
    align-items: center;
  }

  .file-details p {
    margin: 4px 0;
    font-size: 12px;
    color: #666;
  }

  .upload-progress {
    margin-top: 24px;
  }

  .ant-upload-dragger {
    border-radius: 8px;
  }
</style>

<style scoped>
  .document-upload {
    padding: 24px;
  }

  .upload-sections {
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .upload-section {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
  }

  .section-header {
    margin-bottom: 16px;
  }

  .section-header h4 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .upload-content {
    min-height: 120px;
  }

  .uploaded-file {
    max-width: 400px;
  }

  .file-info {
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  .file-details {
    margin: 0;
  }

  .file-details p {
    margin: 4px 0;
    color: #666;
    font-size: 12px;
  }

  .upload-area .ant-upload-dragger {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    min-height: 120px;
  }

  .upload-area .ant-upload-dragger:hover {
    border-color: #40a9ff;
  }

  .upload-progress {
    position: fixed;
    top: 100px;
    right: 24px;
    width: 320px;
    z-index: 1000;
  }

  .ant-upload-hint {
    color: #999;
    font-size: 12px;
  }
</style>
