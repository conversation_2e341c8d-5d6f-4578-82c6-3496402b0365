<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.tsa.student.module.business.major.dao.StudentMajorChoiceDao">

    <select id="getByStudentId" resultType="cn.edu.xmut.tsa.student.module.business.major.domain.vo.StudentMajorChoiceVO">
        SELECT 
            smc.choice_id,
            smc.student_id,
            smc.subject_type,
            smc.first_major_id,
            ep1.major_name AS first_major_name,
            ep1.college_name AS first_college_name,
            smc.second_major_id,
            ep2.major_name AS second_major_name,
            ep2.college_name AS second_college_name,
            smc.third_major_id,
            ep3.major_name AS third_major_name,
            ep3.college_name AS third_college_name,
            smc.create_time,
            smc.update_time
        FROM t_student_major_choice smc
        LEFT JOIN t_enrollment_plan ep1 ON smc.first_major_id = ep1.enrollment_plan_id AND ep1.deleted_flag = 0
        LEFT JOIN t_enrollment_plan ep2 ON smc.second_major_id = ep2.enrollment_plan_id AND ep2.deleted_flag = 0
        LEFT JOIN t_enrollment_plan ep3 ON smc.third_major_id = ep3.enrollment_plan_id AND ep3.deleted_flag = 0
        WHERE smc.student_id = #{studentId}
          AND smc.deleted_flag = 0
    </select>

    <select id="queryEnrollmentPlanBySubjectType" resultType="cn.edu.xmut.tsa.student.module.business.major.domain.vo.EnrollmentPlanStudentVO">
        SELECT 
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            remark
        FROM t_enrollment_plan
        WHERE subject_type = #{subjectType}
          AND year = #{year}
          AND deleted_flag = 0
        ORDER BY sort ASC, major_name ASC
    </select>

    <select id="countByStudentId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_student_major_choice
        WHERE student_id = #{studentId}
          AND deleted_flag = 0
    </select>

</mapper>
