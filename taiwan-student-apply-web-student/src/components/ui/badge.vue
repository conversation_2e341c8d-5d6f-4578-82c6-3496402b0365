<script setup>
  import { cn } from '/@/lib/utils.js';

  const props = defineProps({
    color: {
      type: String,
      default: 'default',
    },
    class: String,
  });

  const getColorClasses = () => {
    const colorMap = {
      default: 'bg-secondary text-secondary-foreground',
      processing: 'bg-blue-100 text-blue-800 border-blue-200',
      success: 'bg-green-100 text-green-800 border-green-200',
      warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      error: 'bg-red-100 text-red-800 border-red-200',
      primary: 'bg-primary text-primary-foreground',
    };
    return colorMap[props.color] || colorMap.default;
  };
</script>

<template>
  <span
    :class="
      cn(
        'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
        getColorClasses(),
        props.class
      )
    "
  >
    <slot />
  </span>
</template>
