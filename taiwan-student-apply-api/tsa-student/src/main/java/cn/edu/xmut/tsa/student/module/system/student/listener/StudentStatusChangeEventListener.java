package cn.edu.xmut.tsa.student.module.system.student.listener;

import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEvent;
import cn.edu.xmut.tsa.student.module.system.student.manager.StudentStatusManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 学生状态变更事件监听器
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Component
public class StudentStatusChangeEventListener {

    @Resource
    private StudentStatusManager studentStatusManager;

    /**
     * 处理学生状态变更事件
     *
     * @param event 学生状态变更事件
     */
    @Async
    @EventListener
    public void handleStudentStatusChangeEvent(StudentStatusChangeEvent event) {
        try {
            log.debug("接收到学生状态变更事件, studentId: {}, eventType: {}", 
                event.getStudentId(), event.getEventType().getDesc());
            
            // 检查并更新学生状态
            studentStatusManager.checkAndUpdateStudentStatus(event.getStudentId());
            
        } catch (Exception e) {
            log.error("处理学生状态变更事件失败, studentId: {}, eventType: {}", 
                event.getStudentId(), event.getEventType().getDesc(), e);
        }
    }
}
