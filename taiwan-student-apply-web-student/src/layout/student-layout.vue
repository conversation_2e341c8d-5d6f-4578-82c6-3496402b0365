<template>
  <div class="student-layout">
    <!-- 侧边导航 -->
    <div :class="cn('sider', { 'sider-collapsed': collapsed })">
      <div class="logo">
        <h3 v-show="!collapsed">台湾学生申请</h3>
        <h3 v-show="collapsed">台</h3>
      </div>

      <nav class="menu">
        <div
          v-for="item in menuItems"
          :key="item.key"
          :class="cn('menu-item', { 'menu-item-active': selectedKeys.includes(item.key) })"
          @click="handleMenuClick(item.key)"
        >
          <component :is="item.icon" class="menu-icon" />
          <span v-show="!collapsed" class="menu-title">{{ item.title }}</span>
        </div>
      </nav>
    </div>

    <!-- 主内容区 -->
    <a-layout>
      <!-- 顶部工具栏 -->
      <a-layout-header class="header">
        <div class="header-left">
          <a-button type="text" @click="collapsed = !collapsed" style="font-size: 16px; width: 64px; height: 64px">
            <template #icon>
              <MenuUnfoldOutlined v-if="collapsed" />
              <MenuFoldOutlined v-else />
            </template>
          </a-button>

          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item>
              <HomeOutlined />
              <span>首页</span>
            </a-breadcrumb-item>
            <a-breadcrumb-item>{{ getCurrentTitle() }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <a-space>
            <a-tag :color="getStatusColor()">
              {{ getStatusDesc() }}
            </a-tag>
            <a-dropdown>
              <a-button type="text">
                <template #icon>
                  <UserOutlined />
                </template>
                用户菜单
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="profile">
                    <UserOutlined />
                    个人信息
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout" @click="handleLogout">
                    <LogoutOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </a-layout-header>

      <!-- 内容区 -->
      <a-layout-content class="content">
        <!-- 进度指示器 - 只在申请流程页面显示 -->
        <div v-if="showProgressIndicator" class="progress-indicator">
          <div class="progress-header">
            <a-steps :current="currentStepIndex" size="small" :responsive="false">
              <a-step title="个人信息完善" />
              <a-step title="专业选择" />
              <a-step title="文件上传" />
            </a-steps>

            <div class="submit-section">
              <a-space>
                <a-button type="primary" size="large" :loading="submitLoading" :disabled="!canSubmit" @click="handleSubmitApplication">
                  提交申请
                </a-button>
                <a-button @click="refreshAllStatus">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  刷新状态
                </a-button>
              </a-space>
            </div>
          </div>
        </div>

        <router-view />
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useStudentApplicationStore } from '/@/store/modules/student-application';
  import { useUserStore } from '/@/store/modules/system/user';
  import { loginApi } from '/@/api/system/login-api';
  import smartEnums from '/@/constants';
  import { toast } from '/@/lib/toast';
  import { cn } from '/@/lib/utils';

  // 导入 shadcn-vue 组件
  import { Button, Badge } from '/@/components/ui';
  import { Breadcrumb, BreadcrumbItem, Steps, Step } from '/@/components/ui';

  // 图标组件
  const HomeIcon = () => (
    <svg class='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
      <path
        stroke-linecap='round'
        stroke-linejoin='round'
        stroke-width='2'
        d='M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'
      />
    </svg>
  );

  const UserIcon = () => (
    <svg class='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
      <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' />
    </svg>
  );

  const BranchIcon = () => (
    <svg class='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
      <path
        stroke-linecap='round'
        stroke-linejoin='round'
        stroke-width='2'
        d='M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z'
      />
    </svg>
  );

  const FileIcon = () => (
    <svg class='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
      <path
        stroke-linecap='round'
        stroke-linejoin='round'
        stroke-width='2'
        d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
      />
    </svg>
  );

  const route = useRoute();
  const router = useRouter();
  const studentApplicationStore = useStudentApplicationStore();
  const userStore = useUserStore();

  // 响应式数据
  const collapsed = ref(false);
  const selectedKeys = ref([route.path]);
  const submitLoading = ref(false);
  const userMenuVisible = ref(false);

  // 菜单项数据
  const menuItems = [
    { key: '/student/home', title: '首页', icon: HomeIcon },
    { key: '/student/info-complete', title: '个人信息完善', icon: UserIcon },
    { key: '/student/major-choice', title: '专业选择', icon: BranchIcon },
    { key: '/student/document', title: '文件上传', icon: FileIcon },
  ];

  // 用户信息
  const userInfo = computed(() => userStore.userInfo || {});

  // 学生状态 - 从store获取
  const studentStatus = computed(() => studentApplicationStore.studentStatus);

  // 步骤完成状态
  const stepCompletionStatus = computed(() => studentApplicationStore.stepCompletionStatus);

  // 当前步骤索引
  const currentStepIndex = computed(() => {
    const stepMap = {
      '/student/info-complete': 0,
      '/student/major-choice': 1,
      '/student/document': 2,
    };
    return stepMap[route.path] || 0;
  });

  // 是否可以提交申请
  const canSubmit = computed(() => {
    return Object.values(stepCompletionStatus.value).every((status) => status);
  });

  // 菜单标题映射
  const menuTitleMap = {
    '/student/home': '首页',
    '/student/info-complete': '个人信息完善',
    '/student/major-choice': '专业选择',
    '/student/document': '文件上传',
  };

  // 是否显示进度指示器 - 只在申请流程页面显示
  const showProgressIndicator = computed(() => {
    return route.path !== '/student/home';
  });

  // 监听路由变化
  watch(
    () => route.path,
    (newPath) => {
      selectedKeys.value = [newPath];
    },
    { immediate: true }
  );

  // 获取当前页面标题
  const getCurrentTitle = () => {
    return menuTitleMap[route.path] || '学生申请';
  };

  // 获取状态颜色
  const getStatusColor = () => {
    const statusMap = {
      0: 'orange',
      1: 'blue',
      2: 'green',
    };
    return statusMap[studentStatus.value] || 'default';
  };

  // 获取状态描述
  const getStatusDesc = () => {
    const statusInfo = Object.values(smartEnums.STUDENT_STATUS_ENUM).find((item) => item.value === studentStatus.value);
    return statusInfo ? statusInfo.desc : '未知状态';
  };

  // 处理菜单点击
  const handleMenuClick = (key) => {
    if (key !== route.path) {
      router.push(key);
    }
  };

  // 切换用户菜单
  const toggleUserMenu = () => {
    userMenuVisible.value = !userMenuVisible.value;
  };

  // 处理用户菜单点击
  const handleUserMenuClick = (key) => {
    userMenuVisible.value = false;
    if (key === 'logout') {
      handleLogout();
    }
  };

  /**
   * 提交申请
   */
  const handleSubmitApplication = async () => {
    submitLoading.value = true;
    try {
      await studentApplicationStore.submitApplication();
      toast.success('申请提交成功！');
    } catch (error) {
      console.error('提交申请失败:', error);
      toast.error(error.message || '提交申请失败，请重试');
    } finally {
      submitLoading.value = false;
    }
  };

  /**
   * 刷新所有状态
   */
  const refreshAllStatus = async () => {
    try {
      await studentApplicationStore.checkAllStepsStatus();
      await studentApplicationStore.getStudentStatus();
      toast.success('状态刷新成功');
    } catch (error) {
      console.error('刷新状态失败:', error);
      toast.error('刷新状态失败');
    }
  };

  /**
   * 退出登录
   */
  const handleLogout = async () => {
    try {
      // 调用退出登录API
      await loginApi.logout();
      // 清除用户信息
      userStore.logout();
      toast.success('已退出登录');
      router.push('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使API调用失败，也清除本地信息
      userStore.logout();
      router.push('/login');
    }
  };

  // 生命周期 - 初始化时获取状态
  onMounted(() => {
    studentApplicationStore.getStudentStatus();
    studentApplicationStore.checkAllStepsStatus();
  });
</script>

<style scoped>
  .student-layout {
    height: 100vh;
    display: flex;
  }

  .sider {
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
    position: relative;
    z-index: 10;
  }

  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
  }

  .logo h3 {
    margin: 0;
    color: #1890ff;
    font-weight: bold;
  }

  .header {
    background: #fff;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
    z-index: 9;
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .breadcrumb {
    margin-left: 16px;
  }

  .header-right {
    padding: 0 24px;
  }

  .content {
    margin: 0;
    padding: 0;
    overflow-y: auto;
    background: #f0f2f5;
  }

  .progress-indicator {
    background-color: #fff;
    padding: 16px 24px;
    margin: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .submit-section {
    margin-left: 24px;
  }

  /* 菜单样式 */
  :deep(.ant-menu-item) {
    margin: 4px 8px;
    border-radius: 4px;
  }

  :deep(.ant-menu-item-selected) {
    background: #e6f7ff;
    border-color: #1890ff;
  }

  :deep(.ant-menu-item-selected::after) {
    border-right: 3px solid #1890ff;
  }
</style>
