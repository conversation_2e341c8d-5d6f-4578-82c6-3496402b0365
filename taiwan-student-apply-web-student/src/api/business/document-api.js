/*
 * 学生资料上传API
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-21 11:00:00
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { getRequest, postRequest } from '/@/lib/axios';

export const documentApi = {
  /**
   * 查询学生资料列表 <AUTHOR>
   */
  queryDocumentList: () => {
    return getRequest('/student/document/list');
  },

  /**
   * 上传学生资料 <AUTHOR>
   */
  uploadDocument: (formData) => {
    return postRequest('/student/document/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * 删除学生资料 <AUTHOR>
   */
  deleteDocument: (documentId) => {
    return postRequest(`/student/document/delete/${documentId}`);
  },

  /**
   * 下载学生资料 <AUTHOR>
   */
  downloadDocument: (documentId) => {
    return getRequest(`/student/document/download/${documentId}`, {
      responseType: 'blob',
    });
  },

  /**
   * 获取资料类型枚举 <AUTHOR>
   */
  getDocumentTypes: () => {
    return getRequest('/student/document/types');
  },
};
