<script setup>
  import { cn } from '/@/lib/utils.js';

  const props = defineProps({
    current: {
      type: Number,
      default: 0,
    },
    size: {
      type: String,
      default: 'default',
    },
    responsive: {
      type: Boolean,
      default: true,
    },
    class: String,
  });

  const getSizeClasses = () => {
    const sizeMap = {
      small: 'text-sm',
      default: 'text-base',
      large: 'text-lg',
    };
    return sizeMap[props.size] || sizeMap.default;
  };
</script>

<template>
  <div :class="cn('flex items-center', getSizeClasses(), props.class)">
    <slot />
  </div>
</template>
