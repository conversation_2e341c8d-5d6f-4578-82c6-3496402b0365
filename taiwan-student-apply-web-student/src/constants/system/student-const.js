/*
 * 学生相关常量
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-21 11:00:00
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

// 性别枚举
export const GENDER_ENUM = {
  MALE: 1,
  FEMALE: 2,
};

// 性别选项
export const GENDER_OPTIONS = [
  { label: '男', value: GENDER_ENUM.MALE },
  { label: '女', value: GENDER_ENUM.FEMALE },
];

// 学生状态枚举
export const STUDENT_STATUS_ENUM = {
  PENDING: 0, // 资料待完善
  REVIEWING: 1, // 资料审核中
  APPROVED: 2, // 审核通过
};

// 学生状态选项
export const STUDENT_STATUS_OPTIONS = [
  { label: '资料待完善', value: STUDENT_STATUS_ENUM.PENDING, color: 'orange' },
  { label: '资料审核中', value: STUDENT_STATUS_ENUM.REVIEWING, color: 'blue' },
  { label: '审核通过', value: STUDENT_STATUS_ENUM.APPROVED, color: 'green' },
];
