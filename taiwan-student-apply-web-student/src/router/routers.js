/*
 * 所有路由入口 - 简化版本，只保留登录功能
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-09-06 20:52:26
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { loginRouters } from './system/login';
import { studentRouters } from './business/student';
import NotFound from '/@/views/system/40X/404.vue';
import NoPrivilege from '/@/views/system/40X/403.vue';

export const routerArray = [
  ...loginRouters,
  ...studentRouters,
  // 默认重定向到学生信息完善页面
  { path: '/', redirect: '/student/info-complete' },
  { path: '/:pathMatch(.*)*', name: '404', component: NotFound },
  { path: '/403', name: '403', component: NoPrivilege },
];
