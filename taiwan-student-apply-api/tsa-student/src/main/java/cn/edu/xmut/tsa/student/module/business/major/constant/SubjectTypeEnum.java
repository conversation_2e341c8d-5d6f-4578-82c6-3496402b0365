package cn.edu.xmut.tsa.student.module.business.major.constant;

import cn.edu.xmut.tsa.base.common.enumeration.BaseEnum;

/**
 * 科类枚举
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
public enum SubjectTypeEnum implements BaseEnum {

    /**
     * 文史科
     */
    LIBERAL_ARTS(1, "文科"),

    /**
     * 理工科
     */
    SCIENCE_ENGINEERING(5, "理科");

    private final Integer value;
    private final String desc;

    SubjectTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
