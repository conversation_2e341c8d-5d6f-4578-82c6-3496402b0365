/*
 * 学生信息API
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-20 11:00:00
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { getRequest, postRequest } from '/@/lib/axios';

export const studentApi = {
  /**
   * 获取当前学生信息 <AUTHOR>
   */
  getStudentInfo: () => {
    return getRequest('/student/info');
  },

  /**
   * 更新学生个人信息 <AUTHOR>
   */
  updateStudentInfo: (form) => {
    return postRequest('/student/info/update', form);
  },

  /**
   * 上传学生头像 <AUTHOR>
   */
  uploadAvatar: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return postRequest('/student/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};
