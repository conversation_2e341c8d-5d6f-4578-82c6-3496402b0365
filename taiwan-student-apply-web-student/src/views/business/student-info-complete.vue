<template>
  <div class="student-info-complete">
    <!-- 页面头部 -->
    <div class="page-header">
      <Card class="mb-4">
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle>个人信息完善</CardTitle>
              <p class="text-sm text-muted-foreground mt-1">请完善您的个人信息和教育经历</p>
            </div>
            <Badge :color="getStatusColor(studentStatus)">
              {{ getStatusDesc(studentStatus) }}
            </Badge>
          </div>
        </CardHeader>
      </Card>
    </div>

    <!-- 个人信息表单 -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle>基本信息</CardTitle>
      </CardHeader>
      <CardContent>
        <Form @submit="handlePersonalSubmit" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem>
              <Label for="realName">姓名</Label>
              <Input id="realName" v-model="personalForm.realName" placeholder="请输入姓名" :class="{ 'border-red-500': personalErrors.realName }" />
              <div v-if="personalErrors.realName" class="text-red-500 text-sm mt-1">{{ personalErrors.realName }}</div>
            </FormItem>

            <FormItem>
              <Label for="gender">性别</Label>
              <Select
                id="gender"
                v-model="personalForm.gender"
                placeholder="请选择性别"
                :options="[
                  { value: 1, label: '男' },
                  { value: 2, label: '女' },
                ]"
              />
              <div v-if="personalErrors.gender" class="text-red-500 text-sm mt-1">{{ personalErrors.gender }}</div>
            </FormItem>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem>
              <Label for="birthday">出生日期</Label>
              <DatePicker id="birthday" v-model="personalForm.birthday" placeholder="请选择出生日期" value-format="YYYY-MM-DD" />
              <div v-if="personalErrors.birthday" class="text-red-500 text-sm mt-1">{{ personalErrors.birthday }}</div>
            </FormItem>

            <FormItem>
              <Label for="idCard">身份证号</Label>
              <Input id="idCard" v-model="personalForm.idCard" placeholder="请输入身份证号" :class="{ 'border-red-500': personalErrors.idCard }" />
              <div v-if="personalErrors.idCard" class="text-red-500 text-sm mt-1">{{ personalErrors.idCard }}</div>
            </FormItem>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem>
              <Label for="phone">手机号码</Label>
              <Input id="phone" v-model="personalForm.phone" placeholder="请输入手机号码" :class="{ 'border-red-500': personalErrors.phone }" />
              <div v-if="personalErrors.phone" class="text-red-500 text-sm mt-1">{{ personalErrors.phone }}</div>
            </FormItem>

            <FormItem>
              <Label for="email">电子邮箱</Label>
              <Input
                id="email"
                v-model="personalForm.email"
                placeholder="请输入电子邮箱"
                type="email"
                :class="{ 'border-red-500': personalErrors.email }"
              />
              <div v-if="personalErrors.email" class="text-red-500 text-sm mt-1">{{ personalErrors.email }}</div>
            </FormItem>
          </div>

          <FormItem>
            <Label for="address">通信地址</Label>
            <Input id="address" v-model="personalForm.address" placeholder="请输入通信地址" :class="{ 'border-red-500': personalErrors.address }" />
            <div v-if="personalErrors.address" class="text-red-500 text-sm mt-1">{{ personalErrors.address }}</div>
          </FormItem>

          <div class="flex gap-2">
            <Button type="submit" :disabled="personalSubmitLoading">
              <div v-if="personalSubmitLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              保存基本信息
            </Button>
            <Button type="button" variant="outline" @click="handlePersonalReset">重置</Button>
          </div>
        </Form>
      </CardContent>
    </Card>

    <!-- 教育经历 -->
    <Card>
      <CardHeader>
        <div class="flex items-center justify-between">
          <CardTitle>教育经历</CardTitle>
          <Button @click="showEducationModal">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            添加教育经历
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <!-- 教育经历列表 -->
        <Table :columns="educationColumns" :data-source="educationList" :loading="educationLoading" row-key="educationId">
          <template #startDate="{ record }">
            {{ formatDate(record.startDate) }}
          </template>
          <template #endDate="{ record }">
            {{ formatDate(record.endDate) }}
          </template>
          <template #educationLevel="{ record }">
            {{ getEducationLevelDesc(record.educationLevel) }}
          </template>
          <template #action="{ record }">
            <div class="flex gap-2">
              <Button variant="ghost" size="sm" @click="showEducationModal(record)">编辑</Button>
              <Button variant="ghost" size="sm" @click="confirmDeleteEducation(record.educationId)" class="text-red-600 hover:text-red-700">
                删除
              </Button>
            </div>
          </template>
        </Table>
      </CardContent>
    </Card>

    <!-- 教育经历弹窗 -->
    <Dialog
      v-model:open="educationModalVisible"
      :title="educationIsEdit ? '编辑教育经历' : '添加教育经历'"
      @ok="handleEducationSubmit"
      @cancel="handleEducationCancel"
      :confirm-loading="educationSubmitLoading"
      width="600px"
    >
      <Form class="space-y-4">
        <FormItem>
          <Label for="schoolName">就读学校名称</Label>
          <Input
            id="schoolName"
            v-model="educationForm.schoolName"
            placeholder="请输入就读学校名称"
            :class="{ 'border-red-500': educationErrors.schoolName }"
          />
          <div v-if="educationErrors.schoolName" class="text-red-500 text-sm mt-1">{{ educationErrors.schoolName }}</div>
        </FormItem>

        <FormItem>
          <Label for="country">国家（地区）</Label>
          <Input id="country" v-model="educationForm.country" placeholder="请输入国家或地区" :class="{ 'border-red-500': educationErrors.country }" />
          <div v-if="educationErrors.country" class="text-red-500 text-sm mt-1">{{ educationErrors.country }}</div>
        </FormItem>

        <FormItem>
          <Label for="startDate">开始时间</Label>
          <DatePicker id="startDate" v-model="educationForm.startDate" placeholder="请选择开始时间" value-format="YYYY-MM-DD" />
          <div v-if="educationErrors.startDate" class="text-red-500 text-sm mt-1">{{ educationErrors.startDate }}</div>
        </FormItem>

        <FormItem>
          <Label for="endDate">结束时间</Label>
          <DatePicker id="endDate" v-model="educationForm.endDate" placeholder="请选择结束时间" value-format="YYYY-MM-DD" />
          <div v-if="educationErrors.endDate" class="text-red-500 text-sm mt-1">{{ educationErrors.endDate }}</div>
        </FormItem>

        <FormItem>
          <Label for="educationLevel">受教育程度</Label>
          <Select id="educationLevel" v-model="educationForm.educationLevel" placeholder="请选择受教育程度" :options="educationLevelOptions" />
          <div v-if="educationErrors.educationLevel" class="text-red-500 text-sm mt-1">{{ educationErrors.educationLevel }}</div>
        </FormItem>
      </Form>
    </Dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue';
  import { studentInfoApi } from '/@/api/business/student-info-api';
  import { educationApi } from '/@/api/business/education-api';
  import { useStudentApplicationStore } from '/@/store/modules/student-application';
  import smartEnums from '/@/constants';
  import { toast } from '/@/lib/toast';
  import dayjs from 'dayjs';

  // 导入 shadcn-vue 组件
  import { Card, CardHeader, CardTitle, CardContent } from '/@/components/ui';
  import { Form, FormItem, Label } from '/@/components/ui';
  import { Input, Button, Select, DatePicker, Table, Dialog, Badge } from '/@/components/ui';

  // ------------------------------ 响应式数据 ------------------------------

  const studentApplicationStore = useStudentApplicationStore();

  // 学生状态 - 从store获取
  const studentStatus = computed(() => studentApplicationStore.studentStatus);

  // ------------------------------ 个人信息相关 ------------------------------

  // 个人信息表单
  const personalForm = reactive({
    realName: '',
    gender: undefined,
    birthday: undefined,
    idCard: '',
    phone: '',
    email: '',
    address: '',
  });

  // 个人信息表单校验规则
  const personalRules = {
    realName: [{ required: true, message: '请输入姓名' }],
    gender: [{ required: true, message: '请选择性别' }],
    birthday: [{ required: true, message: '请选择出生日期' }],
    idCard: [{ required: true, message: '请输入身份证号' }],
    phone: [{ required: true, message: '请输入手机号码' }],
    email: [{ required: true, type: 'email', message: '请输入正确的电子邮箱' }],
    address: [{ required: true, message: '请输入通信地址' }],
  };

  // 个人信息提交加载状态
  const personalSubmitLoading = ref(false);

  // 个人信息表单引用
  const personalFormRef = ref();

  // ------------------------------ 教育经历相关 ------------------------------

  // 教育经历列表
  const educationList = ref([]);
  const educationLoading = ref(false);

  // 教育经历表格列定义
  const educationColumns = [
    {
      title: '就读学校名称',
      dataIndex: 'schoolName',
      key: 'schoolName',
    },
    {
      title: '国家（地区）',
      dataIndex: 'country',
      key: 'country',
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      key: 'endDate',
    },
    {
      title: '受教育程度',
      dataIndex: 'educationLevel',
      key: 'educationLevel',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];

  // 教育经历弹窗相关
  const educationModalVisible = ref(false);
  const educationIsEdit = ref(false);
  const educationSubmitLoading = ref(false);

  // 教育经历表单
  const educationForm = reactive({
    educationId: undefined,
    schoolName: '',
    country: '',
    startDate: undefined,
    endDate: undefined,
    educationLevel: undefined,
  });

  // 教育经历表单默认值
  const educationFormDefault = {
    educationId: undefined,
    schoolName: '',
    country: '',
    startDate: undefined,
    endDate: undefined,
    educationLevel: undefined,
  };

  // 教育经历表单校验规则
  const educationRules = {
    schoolName: [{ required: true, message: '请输入就读学校名称' }],
    country: [{ required: true, message: '请输入国家或地区' }],
    startDate: [{ required: true, message: '请选择开始时间' }],
    endDate: [{ required: true, message: '请选择结束时间' }],
    educationLevel: [{ required: true, message: '请选择受教育程度' }],
  };

  // 教育经历表单引用
  const educationFormRef = ref();

  // ------------------------------ 个人信息方法 ------------------------------

  /**
   * 获取学生个人信息
   */
  const queryStudentInfo = async () => {
    try {
      const res = await studentInfoApi.getStudentInfo();
      if (res.data) {
        Object.assign(personalForm, res.data);
      }
    } catch (error) {
      console.error('获取学生信息失败:', error);
    }
  };

  /**
   * 保存个人信息
   */
  const handlePersonalSubmit = async () => {
    personalSubmitLoading.value = true;
    try {
      await studentInfoApi.updateStudentInfo(personalForm);
      message.success('个人信息保存成功');
      // 刷新个人信息完成状态
      await studentApplicationStore.checkPersonalInfoStatus();
    } catch (error) {
      console.error('保存个人信息失败:', error);
      message.error('保存失败，请重试');
    } finally {
      personalSubmitLoading.value = false;
    }
  };

  /**
   * 重置个人信息表单
   */
  const handlePersonalReset = () => {
    personalFormRef.value.resetFields();
  };

  // ------------------------------ 教育经历方法 ------------------------------

  /**
   * 获取教育经历列表
   */
  const queryEducationList = async () => {
    educationLoading.value = true;
    try {
      const res = await educationApi.queryEducationList();
      educationList.value = res.data || [];
    } catch (error) {
      console.error('获取教育经历失败:', error);
      message.error('获取教育经历失败');
    } finally {
      educationLoading.value = false;
    }
  };

  /**
   * 显示教育经历弹窗
   */
  const showEducationModal = (record) => {
    if (record) {
      // 编辑模式
      educationIsEdit.value = true;
      Object.assign(educationForm, record);
    } else {
      // 新增模式
      educationIsEdit.value = false;
      Object.assign(educationForm, educationFormDefault);
    }
    educationModalVisible.value = true;
  };

  /**
   * 取消教育经历弹窗
   */
  const handleEducationCancel = () => {
    educationModalVisible.value = false;
    Object.assign(educationForm, educationFormDefault);
    educationFormRef.value?.resetFields();
  };

  /**
   * 提交教育经历
   */
  const handleEducationSubmit = async () => {
    try {
      await educationFormRef.value.validate();
      educationSubmitLoading.value = true;

      if (educationIsEdit.value) {
        await educationApi.updateEducation(educationForm);
        message.success('教育经历更新成功');
      } else {
        await educationApi.addEducation(educationForm);
        message.success('教育经历添加成功');
      }

      handleEducationCancel();
      queryEducationList();
      // 刷新教育经历完成状态
      await studentApplicationStore.checkEducationStatus();
    } catch (error) {
      if (error.errorFields) {
        message.error('请检查表单填写');
        return;
      }
      console.error('保存教育经历失败:', error);
      message.error('保存失败，请重试');
    } finally {
      educationSubmitLoading.value = false;
    }
  };

  /**
   * 删除教育经历
   */
  const handleEducationDelete = async (educationId) => {
    try {
      await educationApi.deleteEducation(educationId);
      message.success('删除成功');
      queryEducationList();
      // 刷新教育经历完成状态
      await studentApplicationStore.checkEducationStatus();
    } catch (error) {
      console.error('删除教育经历失败:', error);
      message.error('删除失败，请重试');
    }
  };

  // ------------------------------ 工具方法 ------------------------------

  /**
   * 获取教育程度描述
   */
  const getEducationLevelDesc = (level) => {
    const levelEnum = Object.values(smartEnums.EDUCATION_LEVEL_ENUM).find((item) => item.value === level);
    return levelEnum ? levelEnum.desc : '-';
  };

  /**
   * 获取学生状态描述
   */
  const getStatusDesc = (status) => {
    const statusEnum = Object.values(smartEnums.STUDENT_STATUS_ENUM || {}).find((item) => item.value === status);
    return statusEnum ? statusEnum.desc : '未知状态';
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status) => {
    const colorMap = {
      0: 'default',
      1: 'processing',
      2: 'success',
      3: 'warning',
      4: 'error',
    };
    return colorMap[status] || 'default';
  };

  /**
   * 获取学生状态
   */
  const queryStudentStatus = async () => {
    await studentApplicationStore.getStudentStatus();
  };

  // ------------------------------ 生命周期 ------------------------------

  onMounted(() => {
    queryStudentInfo();
    queryEducationList();
    queryStudentStatus();
  });
</script>

<style scoped>
  .student-info-complete {
    padding: 0 16px 16px 16px;
  }

  .page-header {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .ant-form-item {
    margin-bottom: 16px;
  }
</style>
