/*
 * 学生申请状态管理
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-22
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

import { defineStore } from 'pinia';
import { ref } from 'vue';
import { studentInfoApi } from '/@/api/business/student-info-api';
import { educationApi } from '/@/api/business/education-api';
import { majorChoiceApi } from '/@/api/business/major-choice-api';
import { documentApi } from '/@/api/business/document-api';

export const useStudentApplicationStore = defineStore('studentApplication', () => {
  // ------------------------------ 响应式数据 ------------------------------

  // 学生状态
  const studentStatus = ref(0);

  // 各步骤完成状态
  const stepCompletionStatus = ref({
    personalInfo: false,
    education: false,
    majorChoice: false,
    documentUpload: false,
  });

  // ------------------------------ 状态检查方法 ------------------------------

  /**
   * 检查个人信息完成状态
   */
  const checkPersonalInfoStatus = async () => {
    try {
      const res = await studentInfoApi.getStudentInfo();
      // 检查基本字段是否完成
      const info = res.data;
      const isComplete = info && info.realName && info.gender && info.birthday && info.idCard && info.phone && info.email && info.address;
      stepCompletionStatus.value.personalInfo = !!isComplete;
      return !!isComplete;
    } catch (error) {
      console.error('检查个人信息状态失败:', error);
      return false;
    }
  };

  /**
   * 检查教育经历完成状态
   */
  const checkEducationStatus = async () => {
    try {
      const res = await educationApi.queryEducationList();
      const isComplete = res.data && res.data.length > 0;
      stepCompletionStatus.value.education = isComplete;
      return isComplete;
    } catch (error) {
      console.error('检查教育经历状态失败:', error);
      return false;
    }
  };

  /**
   * 检查专业选择完成状态
   */
  const checkMajorChoiceStatus = async () => {
    try {
      const res = await majorChoiceApi.queryMajorChoice();
      const isComplete = res.data && res.data.firstMajorId;
      stepCompletionStatus.value.majorChoice = isComplete;
      return isComplete;
    } catch (error) {
      console.error('检查专业选择状态失败:', error);
      return false;
    }
  };

  /**
   * 检查文件上传完成状态
   */
  const checkDocumentUploadStatus = async () => {
    try {
      const res = await documentApi.queryDocumentList();
      // 这里应该检查必需的文件类型是否都已上传
      // 假设至少需要上传一个文件
      const isComplete = res.data && res.data.length > 0;
      stepCompletionStatus.value.documentUpload = isComplete;
      return isComplete;
    } catch (error) {
      console.error('检查文件上传状态失败:', error);
      return false;
    }
  };

  /**
   * 检查所有步骤完成状态
   */
  const checkAllStepsStatus = async () => {
    await Promise.all([checkPersonalInfoStatus(), checkEducationStatus(), checkMajorChoiceStatus(), checkDocumentUploadStatus()]);
  };

  /**
   * 获取学生状态
   */
  const getStudentStatus = async () => {
    try {
      const res = await studentInfoApi.getStudentStatus();
      studentStatus.value = res.data || 0;
      return res.data || 0;
    } catch (error) {
      console.error('获取学生状态失败:', error);
      return 0;
    }
  };

  /**
   * 提交申请
   */
  const submitApplication = async () => {
    // 先检查所有步骤是否完成
    await checkAllStepsStatus();

    const allCompleted = Object.values(stepCompletionStatus.value).every((status) => status);
    if (!allCompleted) {
      throw new Error('请完成所有必需步骤后再提交申请');
    }

    try {
      await studentInfoApi.submitApplication();
      await getStudentStatus(); // 刷新状态
    } catch (error) {
      console.error('提交申请失败:', error);
      throw error;
    }
  };

  // ------------------------------ 返回公开API ------------------------------

  return {
    // 状态
    studentStatus,
    stepCompletionStatus,

    // 方法
    checkPersonalInfoStatus,
    checkEducationStatus,
    checkMajorChoiceStatus,
    checkDocumentUploadStatus,
    checkAllStepsStatus,
    getStudentStatus,
    submitApplication,
  };
});
