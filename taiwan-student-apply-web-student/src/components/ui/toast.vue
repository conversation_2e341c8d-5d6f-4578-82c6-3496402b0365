<script setup>
import { ref, onMounted } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps({
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'success', 'error', 'warning'].includes(value)
  },
  title: String,
  description: String,
  duration: {
    type: Number,
    default: 3000
  },
  class: String
})

const emit = defineEmits(['close'])

const visible = ref(true)

onMounted(() => {
  if (props.duration > 0) {
    setTimeout(() => {
      visible.value = false
      emit('close')
    }, props.duration)
  }
})

const getVariantClasses = () => {
  const variants = {
    default: 'bg-background text-foreground border',
    success: 'bg-green-50 text-green-900 border-green-200',
    error: 'bg-red-50 text-red-900 border-red-200',
    warning: 'bg-yellow-50 text-yellow-900 border-yellow-200'
  }
  return variants[props.type] || variants.default
}
</script>

<template>
  <Transition
    enter-active-class="transition-all duration-300 ease-out"
    enter-from-class="opacity-0 transform translate-y-2"
    enter-to-class="opacity-100 transform translate-y-0"
    leave-active-class="transition-all duration-200 ease-in"
    leave-from-class="opacity-100 transform translate-y-0"
    leave-to-class="opacity-0 transform translate-y-2"
  >
    <div
      v-if="visible"
      :class="cn(
        'group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md p-4 pr-6 shadow-lg transition-all',
        getVariantClasses(),
        props.class
      )"
    >
      <div class="grid gap-1">
        <div v-if="title" class="text-sm font-semibold [&+div]:text-xs">
          {{ title }}
        </div>
        <div v-if="description" class="text-sm opacity-90">
          {{ description }}
        </div>
        <slot />
      </div>
      <button
        @click="visible = false; emit('close')"
        class="absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100"
      >
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  </Transition>
</template>
