{"name": "taiwan-student-apply-web-student-shadcn", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.522.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "vue": "^3.5.13"}, "devDependencies": {"@types/node": "^24.0.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}