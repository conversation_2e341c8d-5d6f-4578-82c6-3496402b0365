/*
 * 学生业务相关常量
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-21 11:00:00
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

// 考生状态枚举
export const STUDENT_STATUS_ENUM = {
  PENDING_COMPLETION: {
    value: 0,
    desc: '资料待完善',
    color: 'orange',
  },
  UNDER_REVIEW: {
    value: 1,
    desc: '资料审核中',
    color: 'blue',
  },
  APPROVED: {
    value: 2,
    desc: '审核通过',
    color: 'green',
  },
};

// 资料类型枚举
export const DOCUMENT_TYPE_ENUM = {
  APPLICATION_FORM: {
    value: 1,
    desc: '报名表扫描件',
    required: true,
  },
  TAIWAN_ID: {
    value: 2,
    desc: '台湾居民身份证明扫描件',
    required: true,
  },
  TAIWAN_PERMIT: {
    value: 3,
    desc: '台胞证扫描件',
    required: true,
  },
  GRADUATION_CERTIFICATE: {
    value: 4,
    desc: '高中毕业证书扫描件',
    required: true,
  },
  TRANSCRIPT: {
    value: 5,
    desc: '成绩单扫描件',
    required: true,
  },
  OTHER_CERTIFICATES: {
    value: 6,
    desc: '获奖证书和其他相关证明材料',
    required: false,
  },
};

// 受教育程度枚举
export const EDUCATION_LEVEL_ENUM = {
  HIGH_SCHOOL: {
    value: 'HIGH_SCHOOL',
    desc: '高中',
  },
  VOCATIONAL_SCHOOL: {
    value: 'VOCATIONAL_SCHOOL',
    desc: '职业学校',
  },
  JUNIOR_COLLEGE: {
    value: 'JUNIOR_COLLEGE',
    desc: '专科',
  },
  UNDERGRADUATE: {
    value: 'UNDERGRADUATE',
    desc: '本科',
  },
  GRADUATE: {
    value: 'GRADUATE',
    desc: '研究生',
  },
};

// 文件上传支持的格式
export const UPLOAD_FILE_TYPES = {
  DOCUMENT: ['pdf', 'jpg', 'jpeg', 'png', 'zip', 'rar'],
  IMAGE: ['jpg', 'jpeg', 'png'],
  ARCHIVE: ['zip', 'rar'],
};

// 文件大小限制（字节）
export const FILE_SIZE_LIMIT = {
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  IMAGE: 5 * 1024 * 1024, // 5MB
  ARCHIVE: 20 * 1024 * 1024, // 20MB
};
