<template>
  <div class="student-layout">
    <!-- 侧边导航 -->
    <a-layout-sider v-model:collapsed="collapsed" :trigger=  import { ref, computed, watch, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    UserOutlined,
    BranchesOutlined,
    FileOutlined,
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    HomeOutlined,
    DownOutlined,
    LogoutOutlined,
    ReloadOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { useStudentApplicationStore } from '/@/store/modules/student-application';
  import { useUserStore } from '/@/store/modules/system/user';
  import { loginApi } from '/@/api/system/login-api';
  import smartEnums from '/@/constants';le width="250" theme="light" class="sider">
      <div class="logo">
        <h3 v-show="!collapsed">台湾学生申请</h3>
        <h3 v-show="collapsed">台</h3>
      </div>

      <a-menu v-model:selectedKeys="selectedKeys" mode="inline" :inline-collapsed="collapsed" @click="handleMenuClick">
        <a-menu-item key="/student/info-complete">
          <template #icon>
            <UserOutlined />
          </template>
          <span>个人信息完善</span>
        </a-menu-item>

        <a-menu-item key="/student/major-choice">
          <template #icon>
            <BranchesOutlined />
          </template>
          <span>专业选择</span>
        </a-menu-item>

        <a-menu-item key="/student/document">
          <template #icon>
            <FileOutlined />
          </template>
          <span>文件上传</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>

    <!-- 主内容区 -->
    <a-layout>
      <!-- 顶部工具栏 -->
      <a-layout-header class="header">
        <div class="header-left">
          <a-button type="text" @click="collapsed = !collapsed" style="font-size: 16px; width: 64px; height: 64px">
            <template #icon>
              <MenuUnfoldOutlined v-if="collapsed" />
              <MenuFoldOutlined v-else />
            </template>
          </a-button>

          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item>
              <HomeOutlined />
              <span>首页</span>
            </a-breadcrumb-item>
            <a-breadcrumb-item>{{ getCurrentTitle() }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <a-space>
            <a-tag :color="getStatusColor()">
              {{ getStatusDesc() }}
            </a-tag>
            <a-dropdown>
              <a-button type="text">
                <template #icon>
                  <UserOutlined />
                </template>
                用户菜单
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="profile">
                    <UserOutlined />
                    个人信息
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout" @click="handleLogout">
                    <LogoutOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </a-layout-header>

      <!-- 内容区 -->
      <a-layout-content class="content">
        <!-- 进度指示器 -->
        <div class="progress-indicator">
          <div class="progress-header">
            <a-steps :current="currentStepIndex" size="small" :responsive="false">
              <a-step title="个人信息完善" />
              <a-step title="专业选择" />
              <a-step title="文件上传" />
            </a-steps>

            <div class="submit-section">
              <a-space>
                <a-button type="primary" size="large" :loading="submitLoading" :disabled="!canSubmit" @click="handleSubmitApplication">
                  提交申请
                </a-button>
                <a-button @click="refreshAllStatus">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  刷新状态
                </a-button>
              </a-space>
            </div>
          </div>
        </div>

        <router-view />
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    UserOutlined,
    BranchesOutlined,
    FileOutlined,
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    HomeOutlined,
    DownOutlined,
    LogoutOutlined,
    ReloadOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { useStudentApplicationStore } from '/@/store/modules/student-application';
  import { useUserStore } from '/@/store/modules/system/user';
  import { loginApi } from '/@/api/system/login-api';
  import smartEnums from '/@/constants';

  const route = useRoute();
  const router = useRouter();
  const studentApplicationStore = useStudentApplicationStore();
  const userStore = useUserStore();

  // 响应式数据
  const collapsed = ref(false);
  const selectedKeys = ref([route.path]);
  const submitLoading = ref(false);

  // 学生状态 - 从store获取
  const studentStatus = computed(() => studentApplicationStore.studentStatus);

  // 步骤完成状态
  const stepCompletionStatus = computed(() => studentApplicationStore.stepCompletionStatus);

  // 当前步骤索引
  const currentStepIndex = computed(() => {
    const stepMap = {
      '/student/info-complete': 0,
      '/student/major-choice': 1,
      '/student/document': 2,
    };
    return stepMap[route.path] || 0;
  });

  // 是否可以提交申请
  const canSubmit = computed(() => {
    return Object.values(stepCompletionStatus.value).every((status) => status);
  });

  // 菜单标题映射
  const menuTitleMap = {
    '/student/info-complete': '个人信息完善',
    '/student/major-choice': '专业选择',
    '/student/document': '文件上传',
  };

  // 监听路由变化
  watch(
    () => route.path,
    (newPath) => {
      selectedKeys.value = [newPath];
    },
    { immediate: true }
  );

  // 获取当前页面标题
  const getCurrentTitle = () => {
    return menuTitleMap[route.path] || '学生申请';
  };

  // 获取状态颜色
  const getStatusColor = () => {
    const statusMap = {
      0: 'orange',
      1: 'blue',
      2: 'green',
    };
    return statusMap[studentStatus.value] || 'default';
  };

  // 获取状态描述
  const getStatusDesc = () => {
    const statusInfo = Object.values(smartEnums.STUDENT_STATUS_ENUM).find((item) => item.value === studentStatus.value);
    return statusInfo ? statusInfo.desc : '未知状态';
  };

  // 处理菜单点击
  const handleMenuClick = ({ key }) => {
    if (key !== route.path) {
      router.push(key);
    }
  };

  /**
   * 提交申请
   */
  const handleSubmitApplication = async () => {
    submitLoading.value = true;
    try {
      await studentApplicationStore.submitApplication();
      message.success('申请提交成功！');
    } catch (error) {
      console.error('提交申请失败:', error);
      message.error(error.message || '提交申请失败，请重试');
    } finally {
      submitLoading.value = false;
    }
  };

  /**
   * 刷新所有状态
   */
  const refreshAllStatus = async () => {
    try {
      await studentApplicationStore.checkAllStepsStatus();
      await studentApplicationStore.getStudentStatus();
      message.success('状态刷新成功');
    } catch (error) {
      console.error('刷新状态失败:', error);
      message.error('刷新状态失败');
    }
  };

  /**
   * 退出登录
   */
  const handleLogout = async () => {
    try {
      // 调用退出登录API
      await loginApi.logout();
      // 清除用户信息
      userStore.logout();
      message.success('已退出登录');
      router.push('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      // 即使API调用失败，也清除本地信息
      userStore.logout();
      router.push('/login');
    }
  };

  // 生命周期 - 初始化时获取状态
  onMounted(() => {
    studentApplicationStore.getStudentStatus();
    studentApplicationStore.checkAllStepsStatus();
  });
</script>

<style scoped>
  .student-layout {
    height: 100vh;
    display: flex;
  }

  .sider {
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
    position: relative;
    z-index: 10;
  }

  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
  }

  .logo h3 {
    margin: 0;
    color: #1890ff;
    font-weight: bold;
  }

  .header {
    background: #fff;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
    z-index: 9;
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .breadcrumb {
    margin-left: 16px;
  }

  .header-right {
    padding: 0 24px;
  }

  .content {
    margin: 0;
    padding: 0;
    overflow-y: auto;
    background: #f0f2f5;
  }

  .progress-indicator {
    background-color: #fff;
    padding: 16px 24px;
    margin: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .submit-section {
    margin-left: 24px;
  }

  /* 菜单样式 */
  :deep(.ant-menu-item) {
    margin: 4px 8px;
    border-radius: 4px;
  }

  :deep(.ant-menu-item-selected) {
    background: #e6f7ff;
    border-color: #1890ff;
  }

  :deep(.ant-menu-item-selected::after) {
    border-right: 3px solid #1890ff;
  }
</style>
