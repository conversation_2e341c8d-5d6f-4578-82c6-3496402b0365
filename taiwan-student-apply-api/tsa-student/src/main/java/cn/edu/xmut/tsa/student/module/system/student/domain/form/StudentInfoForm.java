package cn.edu.xmut.tsa.student.module.system.student.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDate;

/**
 * 学生个人信息表单
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Schema(description = "学生个人信息表单")
public class StudentInfoForm {

    /**
     * 头像
     */
    @Schema(description = "头像")
    @NotBlank(message = "头像不能为空")
    private String avatar;

    /**
     * 性别：1-男，2-女
     */
    @Schema(description = "性别：1-男，2-女")
    @NotNull(message = "性别不能为空")
    private Integer gender;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    @NotBlank(message = "手机号码不能为空")
    private String phone;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 台湾居民往来大陆通行证号码
     */
    @Schema(description = "台湾居民往来大陆通行证号码")
    @NotBlank(message = "台湾居民往来大陆通行证号码不能为空")
    @Length(max = 20, message = "台湾居民往来大陆通行证号码长度不能超过20位")
    private String taiwanPassportNumber;

    /**
     * 台湾身份证号码
     */
    @Schema(description = "台湾身份证号码")
    @NotBlank(message = "台湾身份证号码不能为空")
    @Length(max = 20, message = "台湾身份证号码长度不能超过20位")
    private String taiwanIdCardNumber;

    /**
     * 出生地点
     */
    @Schema(description = "出生地点")
    @NotBlank(message = "出生地点不能为空")
    @Length(max = 100, message = "出生地点长度不能超过100位")
    private String birthPlace;

    /**
     * 出生年月日
     */
    @Schema(description = "出生年月日")
    @NotNull(message = "出生年月日不能为空")
    private LocalDate birthDate;

    /**
     * 邮寄地址
     */
    @Schema(description = "邮寄地址")
    @NotBlank(message = "邮寄地址不能为空")
    @Length(max = 200, message = "邮寄地址长度不能超过200位")
    private String mailingAddress;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    /**
     * 应急联系人姓名
     */
    @Schema(description = "应急联系人姓名")
    @NotBlank(message = "应急联系人姓名不能为空")
    @Length(max = 20, message = "应急联系人姓名长度不能超过20位")
    private String emergencyContactName;

    /**
     * 应急联系人电话
     */
    @Schema(description = "应急联系人电话")
    @NotBlank(message = "应急联系人电话不能为空")
    private String emergencyContactPhone;

    /**
     * 个人陈述
     */
    @Schema(description = "个人陈述")
    @NotBlank(message = "个人陈述不能为空")
    @Length(max = 1000, message = "个人陈述长度不能超过1000位")
    private String personalStatement;
}
