/*
 * 学生业务模块路由
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-21 11:00:00
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

export const studentRouters = [
  {
    path: '/student',
    name: 'Student',
    component: () => import('/@/layout/student-layout.vue'),
    redirect: '/student/home',
    meta: {
      title: '学生申请',
      requireAuth: true,
    },
    children: [
      {
        path: '/student/home',
        name: 'StudentHome',
        component: () => import('/@/views/system/home/<USER>'),
        meta: {
          title: '首页',
          requireAuth: true,
        },
      },
      {
        path: '/student/info-complete',
        name: 'StudentInfoComplete',
        component: () => import('/@/views/business/student-info-complete.vue'),
        meta: {
          title: '个人信息完善',
          requireAuth: true,
        },
      },
      {
        path: '/student/major-choice',
        name: 'MajorChoice',
        component: () => import('/@/views/business/major-choice.vue'),
        meta: {
          title: '专业选择',
          requireAuth: true,
        },
      },
      {
        path: '/student/document',
        name: 'DocumentUpload',
        component: () => import('/@/views/business/document-upload.vue'),
        meta: {
          title: '文件上传',
          requireAuth: true,
        },
      },
    ],
  },
];
