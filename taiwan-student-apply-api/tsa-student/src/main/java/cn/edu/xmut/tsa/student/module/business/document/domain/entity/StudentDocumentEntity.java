package cn.edu.xmut.tsa.student.module.business.document.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 学生资料上传实体类
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_student_document")
public class StudentDocumentEntity {

    /**
     * 资料ID
     */
    @TableId(type = IdType.AUTO)
    private Long documentId;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 资料类型：1-报名表扫描件，2-台湾居民身份证明扫描件，3-台胞证扫描件，4-高中毕业证书扫描件，5-成绩单扫描件，6-获奖证书和其他相关证明材料
     */
    private Integer documentType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 是否删除：0-否，1-是
     */
    private Boolean deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
