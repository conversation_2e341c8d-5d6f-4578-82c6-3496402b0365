package cn.edu.xmut.tsa.student.module.business.document.constant;

import cn.edu.xmut.tsa.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资料类型枚举
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Getter
@AllArgsConstructor
public enum DocumentTypeEnum implements BaseEnum {

    /**
     * 报名表扫描件（需考生本人签字）
     */
    APPLICATION_FORM(1, "报名表扫描件（需考生本人签字）", true),

    /**
     * 有效台湾居民身份证明扫描件（双面扫描打包上传）
     */
    TAIWAN_ID_CARD(2, "有效台湾居民身份证明扫描件（双面扫描打包上传）", true),

    /**
     * 台胞证扫描件（双面扫描打包上传）
     */
    TAIWAN_PASSPORT(3, "台胞证扫描件（双面扫描打包上传）", true),

    /**
     * 高中毕业证书扫描件（注明学籍号和台胞证号）
     */
    HIGH_SCHOOL_DIPLOMA(4, "高中毕业证书扫描件（注明学籍号和台胞证号）", true),

    /**
     * 成绩单扫描件（注明学籍号和台胞证号）
     */
    TRANSCRIPT(5, "成绩单扫描件（注明学籍号和台胞证号）", true),

    /**
     * 获奖证书和其他相关证明材料复印件（可选）
     */
    AWARDS_CERTIFICATES(6, "获奖证书和其他相关证明材料复印件（可选）", false);

    private final Integer value;
    private final String desc;
    private final Boolean required;

    public static DocumentTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (DocumentTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 获取所有必需上传的资料类型
     */
    public static DocumentTypeEnum[] getRequiredTypes() {
        return new DocumentTypeEnum[]{
                APPLICATION_FORM,
                TAIWAN_ID_CARD,
                TAIWAN_PASSPORT,
                HIGH_SCHOOL_DIPLOMA,
                TRANSCRIPT
        };
    }
}
