<script setup>
  import { cn } from '/@/lib/utils.js';

  const props = defineProps({
    modelValue: [String, Date],
    placeholder: String,
    disabled: Boolean,
    class: String,
    valueFormat: {
      type: String,
      default: 'YYYY-MM-DD',
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const handleChange = (event) => {
    const value = event.target.value;
    emit('update:modelValue', value);
  };

  const formatValue = (value) => {
    if (!value) return '';
    if (typeof value === 'string') return value;
    if (value instanceof Date) {
      return value.toISOString().split('T')[0];
    }
    return '';
  };
</script>

<template>
  <input
    type="date"
    :value="formatValue(modelValue)"
    :disabled="disabled"
    :placeholder="placeholder"
    :class="
      cn(
        'flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        props.class
      )
    "
    @change="handleChange"
  />
</template>
