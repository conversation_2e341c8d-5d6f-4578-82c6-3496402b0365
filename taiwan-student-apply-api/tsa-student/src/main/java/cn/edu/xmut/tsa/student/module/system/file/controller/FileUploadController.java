package cn.edu.xmut.tsa.student.module.system.file.controller;

import cn.edu.xmut.tsa.base.common.domain.RequestUser;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartRequestUtil;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileUploadVO;
import cn.edu.xmut.tsa.base.module.support.file.service.IFileStorageService;
import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import cn.edu.xmut.tsa.student.module.system.student.service.StudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

/**
 * 文件上传控制器
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Tag(name = "文件上传")
@RestController
public class FileUploadController {

    @Resource
    private IFileStorageService fileStorageService;

    @Resource
    private StudentService studentService;

    /**
     * 上传学生头像
     */
    @PostMapping("/student/upload/avatar")
    @Operation(summary = "上传学生头像  <AUTHOR>
    public ResponseDTO<FileUploadVO> uploadAvatar(@RequestParam("file") MultipartFile file) {
        RequestUser requestStudent = SmartRequestUtil.getRequestUser();
        if (requestStudent == null) {
            return ResponseDTO.userErrorParam("用户未登录");
        }

        StudentEntity studentEntity = studentService.getById(requestStudent.getUserId());

        // 构建文件存储路径：/taiwan_student_apply/{年份}/{考生姓名}/
        String year = String.valueOf(LocalDateTime.now().getYear());
        String studentName = studentEntity.getActualName();
        String folder = String.format("taiwan_student_apply/%s/%s", year, studentName);

        // 构建文件名：一寸照片_{考生姓名}
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        String newFileName = String.format("一寸照片_%s%s", studentName, fileExtension);

        try {
            ResponseDTO<FileUploadVO> uploadResult = fileStorageService.upload(file, folder);
            if (uploadResult.getOk()) {
                log.info("学生头像上传成功，学生ID：{}，文件路径：{}", requestStudent.getUserId(), uploadResult.getData().getFileUrl());
            }
            return uploadResult;
        } catch (Exception e) {
            log.error("上传学生头像失败", e);
            return ResponseDTO.userErrorParam("上传失败，请稍后重试");
        }
    }
}
