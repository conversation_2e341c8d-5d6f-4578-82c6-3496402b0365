<script setup>
  import { cn } from '/@/lib/utils.js';
  import { inject, computed } from 'vue';

  const props = defineProps({
    title: String,
    description: String,
    class: String,
  });

  // 从父组件获取当前步骤和步骤索引
  const current = inject('stepsCurrent', 0);
  const stepIndex = inject('stepIndex', 0);

  const isActive = computed(() => stepIndex === current);
  const isCompleted = computed(() => stepIndex < current);
  const isLast = inject('isLastStep', false);
</script>

<template>
  <div :class="cn('flex items-center', props.class)">
    <!-- 步骤圆圈 -->
    <div class="flex items-center">
      <div
        :class="
          cn('flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium transition-colors', {
            'bg-primary border-primary text-primary-foreground': isActive,
            'bg-primary border-primary text-primary-foreground': isCompleted,
            'border-muted-foreground text-muted-foreground': !isActive && !isCompleted,
          })
        "
      >
        <svg v-if="isCompleted" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        <span v-else>{{ stepIndex + 1 }}</span>
      </div>

      <!-- 连接线 -->
      <div
        v-if="!isLast"
        :class="
          cn('w-16 h-0.5 ml-2 transition-colors', {
            'bg-primary': isCompleted,
            'bg-muted': !isCompleted,
          })
        "
      />
    </div>

    <!-- 步骤内容 -->
    <div class="ml-3">
      <div
        :class="
          cn('font-medium transition-colors', {
            'text-primary': isActive,
            'text-foreground': isCompleted,
            'text-muted-foreground': !isActive && !isCompleted,
          })
        "
      >
        {{ title }}
      </div>
      <div v-if="description" class="text-sm text-muted-foreground">
        {{ description }}
      </div>
    </div>
  </div>
</template>
