<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.tsa.student.module.business.document.dao.StudentDocumentDao">

    <select id="queryByStudentId" resultType="cn.edu.xmut.tsa.student.module.business.document.domain.entity.StudentDocumentEntity">
        SELECT document_id,
               student_id,
               document_type,
               file_name,
               file_path,
               file_size,
               file_type,
               deleted_flag,
               create_time,
               update_time
        FROM t_student_document
        WHERE student_id = #{studentId}
          AND deleted_flag = 0
        ORDER BY document_type ASC, create_time DESC
    </select>

    <select id="getByStudentIdAndType" resultType="cn.edu.xmut.tsa.student.module.business.document.domain.entity.StudentDocumentEntity">
        SELECT document_id,
               student_id,
               document_type,
               file_name,
               file_path,
               file_size,
               file_type,
               deleted_flag,
               create_time,
               update_time
        FROM t_student_document
        WHERE student_id = #{studentId}
          AND document_type = #{documentType}
          AND deleted_flag = 0
        LIMIT 1
    </select>

    <select id="countRequiredDocumentsByStudentId" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT document_type)
        FROM t_student_document
        WHERE student_id = #{studentId}
          AND document_type IN (1, 2, 3, 4, 5)
          AND deleted_flag = 0
    </select>

</mapper>
