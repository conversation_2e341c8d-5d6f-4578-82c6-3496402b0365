@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--reka-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--reka-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 自定义样式 */
.page-header {
  @apply bg-card border border-border rounded-lg mb-4 p-4;
}

/* 布局样式 */
.student-layout {
  @apply h-screen flex bg-background;
}

.sider {
  @apply w-64 bg-card border-r border-border shadow-sm transition-all duration-300 ease-in-out;
}

.sider-collapsed {
  @apply w-16;
}

.main-layout {
  @apply flex-1 flex flex-col;
}

.header {
  @apply h-16 bg-card border-b border-border px-4 flex items-center justify-between shadow-sm;
}

.header-left {
  @apply flex items-center gap-4;
}

.header-right {
  @apply flex items-center gap-2;
}

.collapse-btn {
  @apply w-10 h-10;
}

.breadcrumb {
  @apply text-sm;
}

.content {
  @apply flex-1 bg-muted/30 overflow-auto p-4;
}

.logo {
  @apply h-16 flex items-center justify-center border-b border-border px-4;
}

.logo h3 {
  @apply text-lg font-semibold text-primary m-0 transition-all duration-300;
}

/* 菜单样式 */
.menu {
  @apply p-2 space-y-1;
}

.menu-item {
  @apply flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors;
}

.menu-item-active {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground;
}

.menu-icon {
  @apply w-4 h-4 flex-shrink-0;
}

.menu-title {
  @apply transition-all duration-300;
}

/* 用户菜单样式 */
.user-dropdown {
  @apply relative;
}

.user-btn {
  @apply flex items-center gap-2 px-3 py-2;
}

.user-menu {
  @apply absolute right-0 top-full mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50;
}

.user-menu-item {
  @apply flex items-center gap-2 px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground cursor-pointer;
}

/* 进度指示器样式 */
.progress-indicator {
  @apply bg-card border border-border rounded-lg p-4 mb-4 shadow-sm;
}

.progress-header {
  @apply flex items-center justify-between gap-4;
}

.submit-section {
  @apply flex items-center gap-2;
}

/* 步骤样式 */
.steps-container {
  @apply flex items-center gap-4;
}

.step-item {
  @apply flex items-center gap-2;
}

.step-circle {
  @apply w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium transition-colors;
}

.step-circle.active {
  @apply bg-primary border-primary text-primary-foreground;
}

.step-circle.completed {
  @apply bg-primary border-primary text-primary-foreground;
}

.step-circle.pending {
  @apply border-muted-foreground text-muted-foreground;
}

.step-connector {
  @apply h-0.5 w-16 transition-colors;
}

.step-connector.completed {
  @apply bg-primary;
}

.step-connector.pending {
  @apply bg-muted;
}

/* 表单样式 */
.form-container {
  @apply max-w-md mx-auto p-6 bg-card rounded-lg border border-border;
}

.form-title {
  @apply text-2xl font-bold text-center mb-6 text-foreground;
}

/* 登录页面样式 */
.login-container {
  @apply min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/50;
}

.login-card {
  @apply w-full max-w-md;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .student-layout {
    @apply flex-col;
  }

  .sider {
    @apply w-full h-auto;
  }

  .content {
    @apply flex-1;
  }
}
