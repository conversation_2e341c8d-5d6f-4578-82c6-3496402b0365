/*
 * 教育情况管理API
 *
 * @Author:    <PERSON><PERSON><PERSON>
 * @Date:      2025-06-21 11:00:00
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { getRequest, postRequest } from '/@/lib/axios';

export const educationApi = {
  /**
   * 查询学生教育情况列表 <AUTHOR>
   */
  queryEducationList: () => {
    return getRequest('/student/education/list');
  },

  /**
   * 新增教育情况 <AUTHOR>
   */
  addEducation: (form) => {
    return postRequest('/student/education/add', form);
  },

  /**
   * 更新教育情况 <AUTHOR>
   */
  updateEducation: (form) => {
    return postRequest('/student/education/update', form);
  },

  /**
   * 删除教育情况 <AUTHOR>
   */
  deleteEducation: (educationId) => {
    return postRequest(`/student/education/delete/${educationId}`);
  },
};
