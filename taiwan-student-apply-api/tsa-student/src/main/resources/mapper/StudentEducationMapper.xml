<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.tsa.student.module.business.education.dao.StudentEducationDao">

    <select id="queryByStudentId" resultType="cn.edu.xmut.tsa.student.module.business.education.domain.entity.StudentEducationEntity">
        SELECT education_id,
               student_id,
               school_name,
               country,
               start_date,
               end_date,
               education_level,
               deleted_flag,
               create_time,
               update_time
        FROM t_student_education
        WHERE student_id = #{studentId}
          AND deleted_flag = 0
        ORDER BY start_date DESC
    </select>

    <select id="countByStudentId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_student_education
        WHERE student_id = #{studentId}
          AND deleted_flag = 0
    </select>

</mapper>
