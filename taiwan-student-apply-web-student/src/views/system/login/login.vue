<!--
  * 登录
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2022-09-12 22:34:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
  *
-->
<template>
  <div class="login-container">
    <Card class="login-card">
      <CardHeader>
        <CardTitle class="login-title">账号登录</CardTitle>
      </CardHeader>
      <CardContent>
        <Form class="login-form" @submit="onLogin">
          <FormItem>
            <Label for="loginName">用户名</Label>
            <Input id="loginName" v-model="loginForm.loginName" placeholder="请输入用户名" :class="{ 'border-red-500': errors.loginName }" />
            <div v-if="errors.loginName" class="text-red-500 text-sm mt-1">{{ errors.loginName }}</div>
          </FormItem>

          <FormItem v-if="emailCodeShowFlag">
            <Label for="emailCode">邮箱验证码</Label>
            <div class="flex gap-2">
              <Input id="emailCode" v-model="loginForm.emailCode" placeholder="请输入邮箱验证码" class="flex-1" />
              <Button type="button" @click="sendSmsCode" :disabled="emailCodeButtonDisabled" class="w-28">
                {{ emailCodeTips }}
              </Button>
            </div>
          </FormItem>

          <FormItem>
            <Label for="password">密码</Label>
            <div class="relative">
              <Input
                id="password"
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                :class="{ 'border-red-500': errors.password }"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                <svg v-if="showPassword" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                  />
                </svg>
              </button>
            </div>
            <div v-if="errors.password" class="text-red-500 text-sm mt-1">{{ errors.password }}</div>
          </FormItem>

          <FormItem>
            <Label for="captchaCode">验证码</Label>
            <div class="flex gap-2 items-center">
              <Input id="captchaCode" v-model="loginForm.captchaCode" placeholder="请输入验证码" class="flex-1" />
              <img v-if="captchaBase64Image" :src="captchaBase64Image" @click="getCaptcha" class="h-9 cursor-pointer border rounded" alt="验证码" />
            </div>
          </FormItem>

          <FormItem>
            <div class="flex items-center space-x-2">
              <input id="rememberPwd" type="checkbox" v-model="rememberPwd" class="rounded border-gray-300" />
              <Label for="rememberPwd" class="text-sm font-normal">记住密码</Label>
            </div>
          </FormItem>

          <FormItem>
            <Button type="submit" class="w-full login-btn"> 登录 </Button>
          </FormItem>

          <FormItem>
            <div class="register-link">
              <span>还没有账号？</span>
              <a @click="goToRegister" class="text-primary hover:underline cursor-pointer">立即注册</a>
            </div>
          </FormItem>
        </Form>
      </CardContent>
    </Card>
  </div>
</template>
<script setup>
  import { onMounted, onUnmounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { loginApi } from '/@/api/system/login-api';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { LOGIN_DEVICE_ENUM } from '/@/constants/system/login-device-const';
  import { useUserStore } from '/@/store/modules/system/user';
  import { buildRoutes } from '/@/router/index';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { encryptData } from '/@/lib/encrypt';
  import { localSave } from '/@/utils/local-util.js';
  import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';
  import { toast } from '/@/lib/toast';

  // 导入 shadcn-vue 组件
  import { Card, CardHeader, CardTitle, CardContent } from '/@/components/ui';
  import { Form, FormItem, Label } from '/@/components/ui';
  import { Input, Button } from '/@/components/ui';

  //--------------------- 登录表单 ---------------------------------

  const loginForm = reactive({
    loginName: '',
    password: '',
    captchaCode: '',
    captchaUuid: '',
    loginDevice: LOGIN_DEVICE_ENUM.PC.value,
  });
  // 表单验证错误状态
  const errors = reactive({
    loginName: '',
    password: '',
    captchaCode: '',
  });

  // 验证表单
  const validateForm = () => {
    errors.loginName = '';
    errors.password = '';
    errors.captchaCode = '';

    let isValid = true;

    if (!loginForm.loginName.trim()) {
      errors.loginName = '用户名不能为空';
      isValid = false;
    }

    if (!loginForm.password.trim()) {
      errors.password = '密码不能为空';
      isValid = false;
    }

    if (!loginForm.captchaCode.trim()) {
      errors.captchaCode = '验证码不能为空';
      isValid = false;
    }

    return isValid;
  };

  const showPassword = ref(false);
  const router = useRouter();
  const formRef = ref();
  const rememberPwd = ref(false);

  onMounted(() => {
    document.onkeyup = (e) => {
      if (e.keyCode === 13) {
        onLogin();
      }
    };
  });

  onUnmounted(() => {
    document.onkeyup = null;
  });

  //登录
  async function onLogin(event) {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      SmartLoading.show();
      // 密码加密
      let encryptPasswordForm = Object.assign({}, loginForm, {
        password: encryptData(loginForm.password),
      });
      const res = await loginApi.login(encryptPasswordForm);
      stopRefreshCaptchaInterval();
      localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
      toast.success('登录成功');
      //更新用户信息到pinia
      useUserStore().setUserLoginInfo(res.data);
      //构建系统的路由
      buildRoutes();
      // 登录成功后跳转到首页
      router.push('/student/home');
    } catch (e) {
      if (e.data && e.data.code !== 0) {
        loginForm.captchaCode = '';
        getCaptcha();
      }
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  //--------------------- 验证码 ---------------------------------

  const captchaBase64Image = ref('');
  async function getCaptcha() {
    try {
      let captchaResult = await loginApi.getCaptcha();
      captchaBase64Image.value = captchaResult.data.captchaBase64Image;
      loginForm.captchaUuid = captchaResult.data.captchaUuid;
      beginRefreshCaptchaInterval(captchaResult.data.expireSeconds);
    } catch (e) {
      console.log(e);
    }
  }

  let refreshCaptchaInterval = null;
  function beginRefreshCaptchaInterval(expireSeconds) {
    if (refreshCaptchaInterval === null) {
      refreshCaptchaInterval = setInterval(getCaptcha, (expireSeconds - 5) * 1000);
    }
  }

  function stopRefreshCaptchaInterval() {
    if (refreshCaptchaInterval != null) {
      clearInterval(refreshCaptchaInterval);
      refreshCaptchaInterval = null;
    }
  }

  onMounted(() => {
    getCaptcha();
  });

  //--------------------- 邮箱验证码 ---------------------------------

  const emailCodeShowFlag = ref(false);
  let emailCodeTips = ref('获取邮箱验证码');
  let emailCodeButtonDisabled = ref(false);
  // 定时器
  let countDownTimer = null;
  // 开始倒计时
  function runCountDown() {
    emailCodeButtonDisabled.value = true;
    let countDown = 60;
    emailCodeTips.value = `${countDown}秒后重新获取`;
    countDownTimer = setInterval(() => {
      if (countDown > 1) {
        countDown--;
        emailCodeTips.value = `${countDown}秒后重新获取`;
      } else {
        clearInterval(countDownTimer);
        emailCodeButtonDisabled.value = false;
        emailCodeTips.value = '获取验证码';
      }
    }, 1000);
  }

  // 发送邮箱验证码
  async function sendSmsCode() {
    try {
      SmartLoading.show();
      await loginApi.sendEmailCode(loginForm.loginName);
      toast.success('验证码发送成功!请登录邮箱查看验证码~');
      runCountDown();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // 跳转到注册页面
  function goToRegister() {
    router.push('/register');
  }
</script>
<style lang="less" scoped>
  @import './login.less';
</style>
