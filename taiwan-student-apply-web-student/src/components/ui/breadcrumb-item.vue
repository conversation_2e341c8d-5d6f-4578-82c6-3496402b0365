<script setup>
  import { cn } from '/@/lib/utils.js';

  const props = defineProps({
    class: String,
  });
</script>

<template>
  <div :class="cn('flex items-center', props.class)">
    <slot />
    <svg class="w-4 h-4 mx-2 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
    </svg>
  </div>
</template>
