{"hash": "f3fd22d2", "configHash": "c9972603", "lockfileHash": "0695cffc", "browserHash": "b9822229", "optimized": {"vue": {"src": "../../.pnpm/vue@3.5.17_typescript@5.8.3/node_modules/vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "dbef2f9f", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../.pnpm/@radix-ui+react-slot@1.2.3_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "021f8d4c", "needsInterop": false}, "class-variance-authority": {"src": "../../.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "2aa95790", "needsInterop": false}, "clsx": {"src": "../../.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "26287cd0", "needsInterop": false}, "tailwind-merge": {"src": "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4d4e12d3", "needsInterop": false}}, "chunks": {"chunk-DEX2RCYB": {"file": "chunk-DEX2RCYB.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}