package cn.edu.xmut.tsa.student.module.business.document.service;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileUploadVO;
import cn.edu.xmut.tsa.base.module.support.file.service.IFileStorageService;
import cn.edu.xmut.tsa.student.module.business.document.constant.DocumentTypeEnum;
import cn.edu.xmut.tsa.student.module.business.document.dao.StudentDocumentDao;
import cn.edu.xmut.tsa.student.module.business.document.domain.entity.StudentDocumentEntity;
import cn.edu.xmut.tsa.student.module.business.document.domain.vo.StudentDocumentVO;
import cn.edu.xmut.tsa.student.module.system.student.dao.StudentDao;
import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEvent;
import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEventType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 学生资料服务
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Service
public class StudentDocumentService {

    @Resource
    private StudentDocumentDao studentDocumentDao;

    @Resource
    private StudentDao studentDao;

    @Resource
    private IFileStorageService fileStorageService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 查询学生资料列表
     *
     * @param studentId 学生ID
     * @return 资料列表
     */
    public ResponseDTO<List<StudentDocumentVO>> queryByStudentId(Long studentId) {
        try {
            List<StudentDocumentEntity> documentList = studentDocumentDao.queryByStudentId(studentId);
            List<StudentDocumentVO> voList = new ArrayList<>();

            // 构建已上传资料的Map
            Map<Integer, StudentDocumentEntity> uploadedMap = new HashMap<>();
            for (StudentDocumentEntity entity : documentList) {
                uploadedMap.put(entity.getDocumentType(), entity);
            }

            // 遍历所有资料类型，构建完整的资料列表
            for (DocumentTypeEnum typeEnum : DocumentTypeEnum.values()) {
                StudentDocumentVO vo = new StudentDocumentVO();
                vo.setDocumentType(typeEnum.getValue());
                vo.setDocumentTypeName(typeEnum.getDesc());
                vo.setRequired(typeEnum.getRequired());

                StudentDocumentEntity entity = uploadedMap.get(typeEnum.getValue());
                if (entity != null) {
                    // 已上传
                    vo = SmartBeanUtil.copy(entity, StudentDocumentVO.class);

                    // 获取文件访问URL
                    if (StringUtils.isNotBlank(entity.getFilePath())) {
                        ResponseDTO<String> fileUrlResponse = fileStorageService.getFileUrl(entity.getFilePath());
                        if (BooleanUtils.isTrue(fileUrlResponse.getOk())) {
                            vo.setFileUrl(fileUrlResponse.getData());
                        }
                    }
                }

                voList.add(vo);
            }

            return ResponseDTO.ok(voList);
        } catch (Exception e) {
            log.error("查询学生资料失败", e);
            return ResponseDTO.userErrorParam("查询失败，请稍后重试");
        }
    }

    /**
     * 上传学生资料
     *
     * @param studentId    学生ID
     * @param documentType 资料类型
     * @param file         上传文件
     * @return 上传结果
     */
    public ResponseDTO<String> uploadDocument(Long studentId, Integer documentType, MultipartFile file) {
        try {
            // 校验资料类型
            DocumentTypeEnum typeEnum = DocumentTypeEnum.getByValue(documentType);
            if (typeEnum == null) {
                return ResponseDTO.userErrorParam("资料类型无效");
            }

            // 校验文件
            if (file == null || file.isEmpty()) {
                return ResponseDTO.userErrorParam("请选择要上传的文件");
            }

            // 校验文件类型
            String originalFilename = file.getOriginalFilename();
            if (StringUtils.isBlank(originalFilename)) {
                return ResponseDTO.userErrorParam("文件名无效");
            }

            String fileExtension = getFileExtension(originalFilename).toLowerCase();
            Set<String> allowedExtensions = Set.of("pdf", "jpg", "jpeg", "png", "zip", "rar");
            if (!allowedExtensions.contains(fileExtension)) {
                return ResponseDTO.userErrorParam("仅支持上传 pdf、jpg、png、zip、rar 等格式文件");
            }

            // 获取学生信息
            StudentEntity studentEntity = studentDao.selectById(studentId);
            if (studentEntity == null) {
                return ResponseDTO.userErrorParam("学生信息不存在");
            }

            // 构建文件存储路径：/taiwan_student_apply/{年份}/{考生姓名}/
            String year = String.valueOf(LocalDateTime.now().getYear());
            String studentName = studentEntity.getActualName();
            String folder = String.format("taiwan_student_apply/%s/%s", year, studentName);

            // 构建文件名
            String fileName = String.format("%s_%s_%s",
                    typeEnum.getDesc().replaceAll("[（）()]", ""),
                    studentName,
                    originalFilename);

            // 上传文件
            ResponseDTO<FileUploadVO> uploadResult = fileStorageService.upload(file, folder);
            if (!BooleanUtils.isTrue(uploadResult.getOk())) {
                return ResponseDTO.error(uploadResult);
            }

            // 新增记录
            StudentDocumentEntity entity = new StudentDocumentEntity();
            entity.setStudentId(studentId);
            entity.setDocumentType(documentType);
            entity.setFileName(fileName);
            entity.setFilePath(uploadResult.getData().getFileUrl());
            entity.setFileSize(file.getSize());
            entity.setFileType(file.getContentType());
            entity.setDeletedFlag(false);
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());
            studentDocumentDao.insert(entity);


            // 发布学生状态变更事件
            publishStatusChangeEvent(studentId, StudentStatusChangeEventType.DOCUMENT_CHANGED);

            return ResponseDTO.ok("文件上传成功");
        } catch (Exception e) {
            log.error("上传学生资料失败", e);
            return ResponseDTO.userErrorParam("上传失败，请稍后重试");
        }
    }

    /**
     * 删除学生资料
     *
     * @param studentId  学生ID
     * @param documentId 资料ID
     * @return 删除结果
     */
    public ResponseDTO<String> deleteDocument(Long studentId, Long documentId) {
        try {
            // 检查资料是否存在且属于当前学生
            StudentDocumentEntity entity = studentDocumentDao.selectById(documentId);
            if (entity == null || entity.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("资料不存在");
            }
            if (!entity.getStudentId().equals(studentId)) {
                return ResponseDTO.userErrorParam("无权限操作此资料");
            }

            // 删除文件
            if (StringUtils.isNotBlank(entity.getFilePath())) {
                try {
                    fileStorageService.delete(entity.getFilePath());
                } catch (Exception e) {
                    log.warn("删除文件失败: {}", entity.getFilePath(), e);
                }
            }

            // 删除数据库记录
            entity.setDeletedFlag(true);
            entity.setUpdateTime(LocalDateTime.now());
            studentDocumentDao.updateById(entity);

            // 发布学生状态变更事件
            publishStatusChangeEvent(studentId, StudentStatusChangeEventType.DOCUMENT_CHANGED);

            return ResponseDTO.ok("资料删除成功");
        } catch (Exception e) {
            log.error("删除学生资料失败", e);
            return ResponseDTO.userErrorParam("删除失败，请稍后重试");
        }
    }

    /**
     * 检查学生是否已上传所有必需资料
     *
     * @param studentId 学生ID
     * @return 是否已上传所有必需资料
     */
    public boolean hasAllRequiredDocuments(Long studentId) {
        Long count = studentDocumentDao.countRequiredDocumentsByStudentId(studentId);
        return count >= DocumentTypeEnum.getRequiredTypes().length;
    }

    /**
     * 发布学生状态变更事件
     *
     * @param studentId 学生ID
     * @param eventType 事件类型
     */
    private void publishStatusChangeEvent(Long studentId, StudentStatusChangeEventType eventType) {
        try {
            StudentStatusChangeEvent event = new StudentStatusChangeEvent(this, studentId, eventType);
            eventPublisher.publishEvent(event);
            log.debug("发布学生状态变更事件成功, studentId: {}, eventType: {}", studentId, eventType.getDesc());
        } catch (Exception e) {
            log.error("发布学生状态变更事件失败, studentId: {}, eventType: {}", studentId, eventType.getDesc(), e);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }
}
