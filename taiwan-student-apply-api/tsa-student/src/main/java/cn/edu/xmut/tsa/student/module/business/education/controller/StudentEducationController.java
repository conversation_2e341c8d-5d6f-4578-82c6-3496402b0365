package cn.edu.xmut.tsa.student.module.business.education.controller;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartRequestUtil;
import cn.edu.xmut.tsa.student.module.business.education.domain.form.StudentEducationAddForm;
import cn.edu.xmut.tsa.student.module.business.education.domain.form.StudentEducationUpdateForm;
import cn.edu.xmut.tsa.student.module.business.education.domain.vo.StudentEducationVO;
import cn.edu.xmut.tsa.student.module.business.education.service.StudentEducationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 教育情况控制器
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Tag(name = "教育情况管理")
@RestController
public class StudentEducationController {

    @Resource
    private StudentEducationService studentEducationService;

    /**
     * 查询学生教育情况列表
     */
    @GetMapping("/student/education/list")
    @Operation(summary = "查询学生教育情况列表  <AUTHOR>
    public ResponseDTO<List<StudentEducationVO>> queryEducationList() {
        return studentEducationService.queryByStudentId(SmartRequestUtil.getRequestUserId());
    }

    /**
     * 新增教育情况
     */
    @PostMapping("/student/education/add")
    @Operation(summary = "新增教育情况  <AUTHOR>
    public ResponseDTO<String> addEducation(@Valid @RequestBody StudentEducationAddForm form) {
        return studentEducationService.addEducation(SmartRequestUtil.getRequestUserId(), form);
    }

    /**
     * 更新教育情况
     */
    @PostMapping("/student/education/update")
    @Operation(summary = "更新教育情况  <AUTHOR>
    public ResponseDTO<String> updateEducation(@Valid @RequestBody StudentEducationUpdateForm form) {
        return studentEducationService.updateEducation(SmartRequestUtil.getRequestUserId(), form);
    }

    /**
     * 删除教育情况
     */
    @PostMapping("/student/education/delete/{educationId}")
    @Operation(summary = "删除教育情况  <AUTHOR>
    public ResponseDTO<String> deleteEducation(@PathVariable Long educationId) {
        return studentEducationService.deleteEducation(SmartRequestUtil.getRequestUserId(), educationId);
    }
}
