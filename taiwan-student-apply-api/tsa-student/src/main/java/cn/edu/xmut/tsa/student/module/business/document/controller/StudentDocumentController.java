package cn.edu.xmut.tsa.student.module.business.document.controller;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartRequestUtil;
import cn.edu.xmut.tsa.student.module.business.document.domain.vo.StudentDocumentVO;
import cn.edu.xmut.tsa.student.module.business.document.service.StudentDocumentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 学生资料控制器
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Tag(name = "学生资料管理")
@RestController
public class StudentDocumentController {

    @Resource
    private StudentDocumentService studentDocumentService;

    /**
     * 查询学生资料列表
     */
    @GetMapping("/student/document/list")
    @Operation(summary = "查询学生资料列表  <AUTHOR>
    public ResponseDTO<List<StudentDocumentVO>> queryDocumentList() {
        return studentDocumentService.queryByStudentId(SmartRequestUtil.getRequestUserId());
    }

    /**
     * 上传学生资料
     */
    @PostMapping("/student/document/upload")
    @Operation(summary = "上传学生资料  <AUTHOR>
    public ResponseDTO<String> uploadDocument(@RequestParam("documentType") Integer documentType,
                                              @RequestParam("file") MultipartFile file) {
        return studentDocumentService.uploadDocument(SmartRequestUtil.getRequestUserId(), documentType, file);
    }

    /**
     * 删除学生资料
     */
    @PostMapping("/student/document/delete/{documentId}")
    @Operation(summary = "删除学生资料  <AUTHOR>
    public ResponseDTO<String> deleteDocument(@PathVariable Long documentId) {
        return studentDocumentService.deleteDocument(SmartRequestUtil.getRequestUserId(), documentId);
    }
}
