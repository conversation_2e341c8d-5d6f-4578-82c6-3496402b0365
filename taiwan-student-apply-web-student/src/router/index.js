/*
 * 路由
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-09-06 20:52:04
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
// 移除nprogress依赖，简化版本不需要进度条
import { createRouter, createWebHashHistory } from 'vue-router';
import { routerArray } from './routers';
import { PAGE_PATH_404, PAGE_PATH_LOGIN } from '/@/constants/common-const';
import { useUserStore } from '/@/store/modules/system/user';
import { localRead } from '/@/utils/local-util';
import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';

export const router = createRouter({
  history: createWebHashHistory(),
  routes: routerArray,
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// ----------------------- 路由加载前 -----------------------
router.beforeEach(async (to, from, next) => {
  // 简化版本：只处理登录逻辑

  // 公共页面，任何时候都可以跳转
  if (to.path === PAGE_PATH_404 || to.path === '/403') {
    next();
    return;
  }

  // 验证登录
  const token = localRead(LocalStorageKeyConst.USER_TOKEN);
  if (!token) {
    useUserStore().logout();
    // 允许访问登录页面和注册页面
    if (to.path === PAGE_PATH_LOGIN || to.path === '/register') {
      next();
    } else {
      next({ path: PAGE_PATH_LOGIN });
    }
    return;
  }

  // 已登录用户访问登录页或注册页，跳转到首页
  if (to.path === PAGE_PATH_LOGIN || to.path === '/register') {
    next({ path: '/student/home' });
    return;
  }

  next();
});

// ----------------------- 路由加载后 -----------------------
router.afterEach(() => {
  // 移除进度条功能
});

// ----------------------- 构建router对象 -----------------------
const routerMap = new Map();

export function buildRoutes(menuRouterList) {
  // 简化版本：只有登录功能，不需要动态路由
  console.log('简化版本：不需要构建动态路由');
}
