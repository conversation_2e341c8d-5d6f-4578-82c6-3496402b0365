package cn.edu.xmut.tsa.student.module.business.education.service;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.student.module.business.education.dao.StudentEducationDao;
import cn.edu.xmut.tsa.student.module.business.education.domain.entity.StudentEducationEntity;
import cn.edu.xmut.tsa.student.module.business.education.domain.form.StudentEducationAddForm;
import cn.edu.xmut.tsa.student.module.business.education.domain.form.StudentEducationUpdateForm;
import cn.edu.xmut.tsa.student.module.business.education.domain.vo.StudentEducationVO;
import cn.edu.xmut.tsa.student.module.system.student.dao.StudentDao;
import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEvent;
import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEventType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 教育情况服务
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Service
public class StudentEducationService {

    @Resource
    private StudentEducationDao studentEducationDao;

    @Resource
    private StudentDao studentDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 查询学生教育情况列表
     *
     * @param studentId 学生ID
     * @return 教育情况列表
     */
    public ResponseDTO<List<StudentEducationVO>> queryByStudentId(Long studentId) {
        try {
            List<StudentEducationEntity> educationList = studentEducationDao.queryByStudentId(studentId);
            List<StudentEducationVO> voList = SmartBeanUtil.copyList(educationList, StudentEducationVO.class);
            return ResponseDTO.ok(voList);
        } catch (Exception e) {
            log.error("查询教育情况失败", e);
            return ResponseDTO.userErrorParam("查询失败，请稍后重试");
        }
    }

    /**
     * 新增教育情况
     *
     * @param studentId 学生ID
     * @param form 新增表单
     * @return 新增结果
     */
    public ResponseDTO<String> addEducation(Long studentId, StudentEducationAddForm form) {
        try {
            // 检查时间逻辑
            if (form.getStartDate().isAfter(form.getEndDate())) {
                return ResponseDTO.userErrorParam("开始时间不能晚于结束时间");
            }

            StudentEducationEntity entity = SmartBeanUtil.copy(form, StudentEducationEntity.class);
            entity.setStudentId(studentId);
            entity.setDeletedFlag(false);
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());

            studentEducationDao.insert(entity);
            
            // 发布学生状态变更事件
            publishStatusChangeEvent(studentId, StudentStatusChangeEventType.EDUCATION_INFO_CHANGED);
            
            return ResponseDTO.ok("教育情况添加成功");
        } catch (Exception e) {
            log.error("新增教育情况失败", e);
            return ResponseDTO.userErrorParam("添加失败，请稍后重试");
        }
    }

    /**
     * 更新教育情况
     *
     * @param studentId 学生ID
     * @param form 更新表单
     * @return 更新结果
     */
    public ResponseDTO<String> updateEducation(Long studentId, StudentEducationUpdateForm form) {
        try {
            // 检查教育情况是否存在且属于当前学生
            StudentEducationEntity existEntity = studentEducationDao.selectById(form.getEducationId());
            if (existEntity == null || existEntity.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("教育情况不存在");
            }
            if (!existEntity.getStudentId().equals(studentId)) {
                return ResponseDTO.userErrorParam("无权限操作此教育情况");
            }

            // 检查时间逻辑
            if (form.getStartDate().isAfter(form.getEndDate())) {
                return ResponseDTO.userErrorParam("开始时间不能晚于结束时间");
            }

            StudentEducationEntity entity = SmartBeanUtil.copy(form, StudentEducationEntity.class);
            entity.setStudentId(studentId);
            entity.setUpdateTime(LocalDateTime.now());

            studentEducationDao.updateById(entity);
            
            // 发布学生状态变更事件
            publishStatusChangeEvent(studentId, StudentStatusChangeEventType.EDUCATION_INFO_CHANGED);
            
            return ResponseDTO.ok("教育情况更新成功");
        } catch (Exception e) {
            log.error("更新教育情况失败", e);
            return ResponseDTO.userErrorParam("更新失败，请稍后重试");
        }
    }

    /**
     * 删除教育情况
     *
     * @param studentId 学生ID
     * @param educationId 教育情况ID
     * @return 删除结果
     */
    public ResponseDTO<String> deleteEducation(Long studentId, Long educationId) {
        try {
            // 检查教育情况是否存在且属于当前学生
            StudentEducationEntity existEntity = studentEducationDao.selectById(educationId);
            if (existEntity == null || existEntity.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("教育情况不存在");
            }
            if (!existEntity.getStudentId().equals(studentId)) {
                return ResponseDTO.userErrorParam("无权限操作此教育情况");
            }

            existEntity.setDeletedFlag(true);
            existEntity.setUpdateTime(LocalDateTime.now());
            studentEducationDao.updateById(existEntity);

            // 发布学生状态变更事件
            publishStatusChangeEvent(studentId, StudentStatusChangeEventType.EDUCATION_INFO_CHANGED);

            return ResponseDTO.ok("教育情况删除成功");
        } catch (Exception e) {
            log.error("删除教育情况失败", e);
            return ResponseDTO.userErrorParam("删除失败，请稍后重试");
        }
    }

    /**
     * 检查学生是否有教育情况
     *
     * @param studentId 学生ID
     * @return 是否有教育情况
     */
    public boolean hasEducation(Long studentId) {
        Long count = studentEducationDao.countByStudentId(studentId);
        return count > 0;
    }

    /**
     * 发布学生状态变更事件
     *
     * @param studentId 学生ID
     * @param eventType 事件类型
     */
    private void publishStatusChangeEvent(Long studentId, StudentStatusChangeEventType eventType) {
        try {
            StudentStatusChangeEvent event = new StudentStatusChangeEvent(this, studentId, eventType);
            eventPublisher.publishEvent(event);
            log.debug("发布学生状态变更事件成功, studentId: {}, eventType: {}", studentId, eventType.getDesc());
        } catch (Exception e) {
            log.error("发布学生状态变更事件失败, studentId: {}, eventType: {}", studentId, eventType.getDesc(), e);
        }
    }
}
