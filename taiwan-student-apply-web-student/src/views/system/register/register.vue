<template>
  <div class="register-container">
    <a-card class="register-card" :bordered="false">
      <a-typography-title :level="2" class="register-title">账号注册</a-typography-title>

      <a-form ref="registerFormRef" :model="registerForm" :rules="registerRules" layout="vertical" @finish="onRegister">
        <a-row :gutter="16">
          <a-col :span="12">
            <!-- 邮箱 -->
            <a-form-item label="邮箱地址" name="email">
              <a-input v-model:value="registerForm.email" placeholder="请输入邮箱地址" size="large">
                <template #prefix>
                  <MailOutlined />
                </template>
              </a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <!-- 邮箱验证码 -->
            <a-form-item label="邮箱验证码" name="emailCode">
              <a-input-group compact>
                <a-input style="width: calc(100% - 110px)" v-model:value="registerForm.emailCode" placeholder="请输入邮箱验证码" size="large" />
                <a-button
                  @click="sendEmailCode"
                  type="primary"
                  size="large"
                  :disabled="emailCodeCountdown > 0"
                  style="width: 110px; border-start-start-radius: 0; border-end-start-radius: 0"
                >
                  {{ emailCodeCountdown > 0 ? `${emailCodeCountdown}s` : '获取验证码' }}
                </a-button>
              </a-input-group>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <!-- 真实姓名 -->
            <a-form-item label="真实姓名" name="actualName">
              <a-input v-model:value="registerForm.actualName" placeholder="请输入真实姓名" size="large">
                <template #prefix>
                  <UserOutlined />
                </template>
              </a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <!-- 手机号 -->
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="registerForm.phone" placeholder="请输入手机号" size="large">
                <template #prefix>
                  <PhoneOutlined />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <!-- 密码 -->
            <a-form-item label="密码" name="password">
              <a-input-password v-model:value="registerForm.password" placeholder="请输入密码" size="large">
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input-password>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <!-- 确认密码 -->
            <a-form-item label="确认密码" name="confirmPassword">
              <a-input-password v-model:value="registerForm.confirmPassword" placeholder="请再次输入密码" size="large">
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input-password>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 注册按钮 -->
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="registerLoading" size="large" block> 注册 </a-button>
        </a-form-item>

        <!-- 登录链接 -->
        <a-form-item>
          <a-typography-text class="login-link">
            已有账号？
            <a-button type="link" @click="goToLogin" class="login-btn"> 立即登录 </a-button>
          </a-typography-text>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { encryptData } from '/@/lib/encrypt';
  import { MailOutlined, UserOutlined, PhoneOutlined, LockOutlined } from '@ant-design/icons-vue';
  import { loginApi } from '/@/api/system/login-api';

  // 组件名称
  defineOptions({
    name: 'StudentRegister',
  });

  // 路由
  const router = useRouter();

  // 表单引用
  const registerFormRef = ref();

  // ------------------------------ 注册表单相关 ------------------------------

  // 注册表单数据
  const registerForm = reactive({
    email: '',
    emailCode: '',
    actualName: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });

  // 表单验证规则
  const registerRules = {
    email: [
      { required: true, message: '请输入邮箱地址' },
      { type: 'email', message: '邮箱格式不正确' },
    ],
    emailCode: [
      { required: true, message: '请输入邮箱验证码' },
      { pattern: /^\d{6}$/, message: '邮箱验证码为6位数字' },
    ],
    actualName: [{ required: true, message: '请输入真实姓名' }],
    phone: [
      { required: true, message: '请输入手机号' },
      { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
    ],
    password: [
      { required: true, message: '请输入密码' },
      { min: 8, message: '密码长度至少8位' },
    ],
    confirmPassword: [
      { required: true, message: '请再次输入密码' },
      {
        validator: (rule, value) => {
          if (value && value !== registerForm.password) {
            return Promise.reject('两次输入的密码不一致');
          }
          return Promise.resolve();
        },
      },
    ],
  };

  // 注册加载状态
  const registerLoading = ref(false);

  // ------------------------------ 邮箱验证码相关 ------------------------------

  // 邮箱验证码发送状态
  const emailCodeCountdown = ref(0);

  // 发送邮箱验证码
  async function sendEmailCode() {
    try {
      // 验证邮箱字段
      await registerFormRef.value.validateFields(['email']);
    } catch (error) {
      message.error('请输入正确的邮箱地址');
    }
    try {
      SmartLoading.show();
      await loginApi.sendEmailCode({
        email: registerForm.email,
      });
      message.success('验证码已发送到您的邮箱');

      // 开始倒计时
      emailCodeCountdown.value = 60;
      const timer = setInterval(() => {
        emailCodeCountdown.value--;
        if (emailCodeCountdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    } finally {
      SmartLoading.hide();
    }
  }

  // ------------------------------ 注册相关 ------------------------------

  // 注册
  async function onRegister() {
    try {
      // 表单验证
      await registerFormRef.value.validate();
    } catch (error) {
      message.error('请检查表单信息');
    }
    registerLoading.value = true;
    try {
      let encryptPasswordForm = Object.assign({}, registerForm, {
        password: encryptData(registerForm.password),
        confirmPassword: encryptData(registerForm.confirmPassword),
      });
      await loginApi.register(encryptPasswordForm);
      message.success('注册成功，请登录');
      router.push('/login');
    } finally {
      registerLoading.value = false;
    }
  }

  // 跳转到登录页面
  function goToLogin() {
    router.push('/login');
  }
</script>

<style lang="less" scoped>
  .register-container {
    width: 100%;
    height: 100vh;
    background: url(/@/assets/images/login/login-bg2.png) no-repeat center;
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;

    .register-card {
      width: 100%;
      max-width: 600px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      border-radius: 12px;

      .register-title {
        text-align: center;
        margin-bottom: 32px !important;
        color: #1e1e1e;
      }

      .login-link {
        text-align: center;
        display: block;

        .login-btn {
          padding: 0;
          height: auto;
          font-size: 14px;
        }
      }
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    .register-container {
      padding: 10px;

      .register-card {
        margin: 0;
      }
    }

    :deep(.ant-col) {
      width: 100% !important;
      max-width: 100% !important;
      flex: 0 0 100% !important;
    }
  }
</style>
