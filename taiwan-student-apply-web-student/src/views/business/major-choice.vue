<template>
  <div class="major-choice">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-page-header title="专业选择" sub-title="请选择您感兴趣的专业志愿" />
    </div>

    <a-card title="专业志愿选择" :bordered="false">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" @finish="handleSubmit">
        <a-form-item label="科类" name="subjectType">
          <a-select v-model:value="formData.subjectType" placeholder="请选择科类" @change="onSubjectTypeChange" style="width: 300px">
            <a-select-option value="1">理工类</a-select-option>
            <a-select-option value="2">文史类</a-select-option>
            <a-select-option value="3">艺术类</a-select-option>
            <a-select-option value="4">体育类</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 第一专业志愿 -->
        <a-form-item label="第一专业志愿" name="firstMajorId">
          <a-select
            v-model:value="formData.firstMajorId"
            placeholder="请选择第一专业志愿"
            :loading="majorLoading"
            :disabled="!formData.subjectType"
            show-search
            :filter-option="filterOption"
            style="width: 100%"
          >
            <a-select-option v-for="major in availableMajors" :key="major.majorId" :value="major.majorId">
              {{ major.majorName }} ({{ major.collegeName }})
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 第二专业志愿 -->
        <a-form-item label="第二专业志愿" name="secondMajorId">
          <a-select
            v-model:value="formData.secondMajorId"
            placeholder="请选择第二专业志愿"
            :loading="majorLoading"
            :disabled="!formData.subjectType"
            show-search
            :filter-option="filterOption"
            style="width: 100%"
          >
            <a-select-option v-for="major in getFilteredMajors('second')" :key="major.majorId" :value="major.majorId">
              {{ major.majorName }} ({{ major.collegeName }})
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 第三专业志愿 -->
        <a-form-item label="第三专业志愿" name="thirdMajorId">
          <a-select
            v-model:value="formData.thirdMajorId"
            placeholder="请选择第三专业志愿"
            :loading="majorLoading"
            :disabled="!formData.subjectType"
            show-search
            :filter-option="filterOption"
            style="width: 100%"
          >
            <a-select-option v-for="major in getFilteredMajors('third')" :key="major.majorId" :value="major.majorId">
              {{ major.majorName }} ({{ major.collegeName }})
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 4, span: 20 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitLoading"> 保存专业志愿 </a-button>
            <a-button @click="handleReset"> 重置 </a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 专业志愿预览 -->
      <a-divider />
      <div v-if="hasChoices" class="choice-preview">
        <h4>当前专业志愿：</h4>
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="科类">
            {{ getSubjectTypeDesc(formData.subjectType) }}
          </a-descriptions-item>
          <a-descriptions-item label="第一专业志愿">
            {{ getMajorDesc(formData.firstMajorId) }}
          </a-descriptions-item>
          <a-descriptions-item label="第二专业志愿">
            {{ getMajorDesc(formData.secondMajorId) }}
          </a-descriptions-item>
          <a-descriptions-item label="第三专业志愿">
            {{ getMajorDesc(formData.thirdMajorId) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-card>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { majorChoiceApi } from '/@/api/business/major-choice-api';
  import { useStudentApplicationStore } from '/@/store/modules/student-application';

  // 响应式数据
  const studentApplicationStore = useStudentApplicationStore();
  const formRef = ref();
  const submitLoading = ref(false);
  const majorLoading = ref(false);
  const availableMajors = ref([]);

  // 表单数据
  const formData = reactive({
    subjectType: '',
    firstMajorId: '',
    secondMajorId: '',
    thirdMajorId: '',
  });

  // 表单验证规则
  const rules = {
    subjectType: [{ required: true, message: '请选择科类', trigger: 'change' }],
    firstMajorId: [{ required: true, message: '请选择第一专业志愿', trigger: 'change' }],
    secondMajorId: [{ required: true, message: '请选择第二专业志愿', trigger: 'change' }],
    thirdMajorId: [{ required: true, message: '请选择第三专业志愿', trigger: 'change' }],
  };

  // 计算是否有选择
  const hasChoices = computed(() => {
    return formData.subjectType && formData.firstMajorId && formData.secondMajorId && formData.thirdMajorId;
  });

  // 科类描述映射
  const subjectTypeMap = {
    1: '理工类',
    2: '文史类',
    3: '艺术类',
    4: '体育类',
  };

  // 获取科类描述
  const getSubjectTypeDesc = (type) => {
    return subjectTypeMap[type] || type;
  };

  // 获取专业描述
  const getMajorDesc = (majorId) => {
    const major = availableMajors.value.find((m) => m.majorId === majorId);
    return major ? `${major.majorName} (${major.collegeName})` : '';
  };

  // 过滤选项函数
  const filterOption = (input, option) => {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 获取过滤后的专业列表（排除已选择的）
  const getFilteredMajors = (position) => {
    return availableMajors.value.filter((major) => {
      if (position === 'second') {
        return major.majorId !== formData.firstMajorId;
      } else if (position === 'third') {
        return major.majorId !== formData.firstMajorId && major.majorId !== formData.secondMajorId;
      }
      return true;
    });
  };

  // 科类变化处理
  const onSubjectTypeChange = (value) => {
    // 清空已选择的专业
    formData.firstMajorId = '';
    formData.secondMajorId = '';
    formData.thirdMajorId = '';

    if (value) {
      queryEnrollmentPlan(value);
    } else {
      availableMajors.value = [];
    }
  };

  // 查询招生计划
  const queryEnrollmentPlan = async (subjectType) => {
    try {
      majorLoading.value = true;
      const res = await majorChoiceApi.queryEnrollmentPlanBySubjectType(subjectType);
      if (res.data) {
        availableMajors.value = res.data;
      }
    } catch (error) {
      message.error('查询招生计划失败');
      availableMajors.value = [];
    } finally {
      majorLoading.value = false;
    }
  };

  // 查询当前学生的专业志愿
  const queryCurrentChoice = async () => {
    try {
      const res = await majorChoiceApi.queryMajorChoice();
      if (res.data) {
        Object.assign(formData, {
          subjectType: res.data.subjectType?.toString() || '',
          firstMajorId: res.data.firstMajorId || '',
          secondMajorId: res.data.secondMajorId || '',
          thirdMajorId: res.data.thirdMajorId || '',
        });

        // 如果有科类，加载对应的招生计划
        if (formData.subjectType) {
          await queryEnrollmentPlan(formData.subjectType);
        }
      }
    } catch (error) {
      // 没有志愿记录是正常的，不显示错误信息
      console.log('暂无专业志愿记录');
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formRef.value.validate();

      // 验证专业志愿不能重复
      const majors = [formData.firstMajorId, formData.secondMajorId, formData.thirdMajorId];
      const uniqueMajors = [...new Set(majors)];
      if (uniqueMajors.length !== majors.length) {
        message.error('专业志愿不能重复选择');
        return;
      }

      submitLoading.value = true;

      const submitData = {
        subjectType: parseInt(formData.subjectType),
        firstMajorId: formData.firstMajorId,
        secondMajorId: formData.secondMajorId,
        thirdMajorId: formData.thirdMajorId,
      };

      await majorChoiceApi.saveMajorChoice(submitData);
      message.success('保存专业志愿成功');
      // 刷新专业选择完成状态
      await studentApplicationStore.checkMajorChoiceStatus();
    } catch (error) {
      if (error.errorFields) {
        // 表单验证失败
        return;
      }
      message.error('保存专业志愿失败');
    } finally {
      submitLoading.value = false;
    }
  };

  // 重置表单
  const handleReset = () => {
    Object.assign(formData, {
      subjectType: '',
      firstMajorId: '',
      secondMajorId: '',
      thirdMajorId: '',
    });
    availableMajors.value = [];
    formRef.value?.resetFields();
  };

  // 监听专业选择变化，自动调整后续选项
  watch(
    () => formData.firstMajorId,
    (newVal, oldVal) => {
      if (oldVal && formData.secondMajorId === newVal) {
        formData.secondMajorId = '';
      }
      if (oldVal && formData.thirdMajorId === newVal) {
        formData.thirdMajorId = '';
      }
    }
  );

  watch(
    () => formData.secondMajorId,
    (newVal, oldVal) => {
      if (oldVal && formData.thirdMajorId === newVal) {
        formData.thirdMajorId = '';
      }
    }
  );

  // 组件挂载时查询当前志愿
  onMounted(() => {
    queryCurrentChoice();
  });
</script>

<style scoped>
  .major-choice {
    padding: 0 16px 16px 16px;
  }

  .page-header {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .choice-preview {
    margin-top: 16px;
  }

  .choice-preview h4 {
    margin-bottom: 16px;
    color: #1890ff;
  }
</style>

<style scoped>
  .major-choice {
    padding: 24px;
  }

  .choice-preview {
    margin-top: 16px;
  }
</style>
