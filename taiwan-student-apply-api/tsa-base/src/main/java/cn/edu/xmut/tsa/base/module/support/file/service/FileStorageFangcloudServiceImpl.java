package cn.edu.xmut.tsa.base.module.support.file.service;

import cn.edu.xmut.tsa.base.common.code.SystemErrorCode;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartStringUtil;
import cn.edu.xmut.tsa.base.constant.RedisKeyConst;
import cn.edu.xmut.tsa.base.module.support.file.constant.FileFolderTypeEnum;
import cn.edu.xmut.tsa.base.module.support.file.dao.FileDao;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileDownloadVO;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileMetadataVO;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileUploadVO;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileVO;
import cn.edu.xmut.tsa.base.module.support.file.util.FangcloudJwtUtil;
import cn.edu.xmut.tsa.base.module.support.redis.RedisService;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 亿方云文件存储服务实现（JWT模式）
 *
 * <AUTHOR>
 * @Date 2025-06-21
 */
@Slf4j
public class FileStorageFangcloudServiceImpl implements IFileStorageService {

    /**
     * 亿方云API基础URL
     */
    private static final String FANGCLOUD_API_BASE_URL = "https://open.fangcloud.com/api/v2";

    /**
     * 亿方云OAuth Token URL
     */
    private static final String FANGCLOUD_OAUTH_URL = "https://oauth.fangcloud.com/oauth/token";

    /**
     * 上传文件接口路径
     */
    private static final String UPLOAD_FILE_PATH = "/file/upload";

    /**
     * 下载文件接口路径
     */
    private static final String DOWNLOAD_FILE_PATH = "/file/download";

    /**
     * 删除文件接口路径
     */
    private static final String DELETE_FILE_PATH = "/file/delete";

    /**
     * 获取文件信息接口路径
     */
    private static final String FILE_INFO_PATH = "/file/{id}/info";

    /**
     * 根据路径获取上传地址接口路径
     */
    private static final String UPLOAD_BY_PATH_URL = "/file/upload_by_path";

    /**
     * JWT过期时间（秒）
     */
    private static final int JWT_EXPIRE_SECONDS = 60;

    @Value("${file.storage.fangcloud.client-id}")
    private String clientId;

    @Value("${file.storage.fangcloud.client-secret}")
    private String clientSecret;

    @Value("${file.storage.fangcloud.enterprise-id}")
    private String enterpriseId;

    @Value("${file.storage.fangcloud.kid}")
    private String kid;

    @Value("${file.storage.fangcloud.folder-id:0}")
    private String defaultFolderId;

    @Value("${file.storage.fangcloud.private-url-expire-seconds:3600}")
    private Long privateUrlExpireSeconds;

    @Resource
    private RedisService redisService;

    @Resource
    private FileDao fileDao;

    /**
     * 缓存的访问令牌
     */
    private final AtomicReference<String> cachedAccessToken = new AtomicReference<>();

    /**
     * 访问令牌过期时间
     */
    private final AtomicReference<Long> tokenExpireTime = new AtomicReference<>(0L);

    @Override
    public ResponseDTO<FileUploadVO> upload(MultipartFile file, String path) {
        // -------------- 1、校验文件基本信息 ------------------------
        if (null == file) {
            return ResponseDTO.userErrorParam("上传文件不能为空");
        }
        String originalFileName = file.getOriginalFilename();
        if (SmartStringUtil.isEmpty(originalFileName)) {
            return ResponseDTO.userErrorParam("上传文件名为空");
        }

        String fileType = FilenameUtils.getExtension(originalFileName);

        // -------------- 2、获取上传URL ------------------------
        try {
            String accessToken = this.getAccessToken();
            if (SmartStringUtil.isEmpty(accessToken)) {
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取访问令牌失败");
            }

            // 构建获取上传URL的参数
            Map<String, Object> uploadParams = new HashMap<>();
            uploadParams.put("name", originalFileName);
            uploadParams.put("target_folder_path", path);
            uploadParams.put("upload_type", "api");

            String getUrlResult = HttpRequest.post(FANGCLOUD_API_BASE_URL + UPLOAD_BY_PATH_URL)
                    .header("Authorization", "Bearer " + accessToken)
                    .body(JSONUtil.toJsonStr(uploadParams))
                    .execute().body();

            JSONObject urlResponse = JSONUtil.parseObj(getUrlResult);
            String presignUrl = urlResponse.getStr("presign_url");
            
            if (SmartStringUtil.isEmpty(presignUrl)) {
                log.error("获取亿方云上传URL失败，返回URL为空: {}", getUrlResult);
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取上传URL失败");
            }

            // -------------- 3、直接使用MultipartFile执行上传 ------------------------
            HttpRequest uploadRequest = HttpUtil.createPost(presignUrl)
                    .form("file", file.getBytes(), originalFileName);

            HttpResponse uploadResponse = uploadRequest.execute();

            if (!uploadResponse.isOk()) {
                log.error("亿方云文件上传失败，HTTP状态码: {}, 响应内容: {}", uploadResponse.getStatus(), uploadResponse.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文件上传失败");
            }

            JSONObject uploadResultJson = JSONUtil.parseObj(uploadResponse.body());
            String fileId = uploadResultJson.getStr("id");
            Long fileSize = uploadResultJson.getLong("size");

            if (SmartStringUtil.isEmpty(fileId)) {
                log.error("亿方云文件上传失败，返回fileId为空: {}", uploadResponse.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文件上传失败");
            }

            // -------------- 4、构建返回结果 ------------------------
            FileUploadVO uploadVO = new FileUploadVO();
            uploadVO.setFileName(originalFileName);
            uploadVO.setFileType(fileType);
            uploadVO.setFileKey(fileId);
            uploadVO.setFileSize(fileSize != null ? fileSize : file.getSize());

            // 根据文件路径确定是否为私有文件
            if (path.startsWith(FileFolderTypeEnum.FOLDER_PRIVATE)) {
                uploadVO.setFileUrl(this.getFileUrl(fileId).getData());
            } else {
                uploadVO.setFileUrl(this.getFileUrl(fileId).getData());
            }

            return ResponseDTO.ok(uploadVO);

        } catch (IOException e) {
            log.error("读取文件内容失败：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文件上传失败");
        } catch (Exception e) {
            log.error("亿方云文件上传发生未知异常：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "上传失败");
        }
    }

    @Override
    public ResponseDTO<String> getFileUrl(String fileKey) {
        if (SmartStringUtil.isEmpty(fileKey)) {
            return ResponseDTO.userErrorParam("文件不存在，key为空");
        }

        // -------------- 1、检查缓存中是否存在URL ------------------------
        String fileRedisKey = RedisKeyConst.Support.FILE_PRIVATE_VO + fileKey;
        FileVO fileVO = redisService.getObject(fileRedisKey, FileVO.class);

        if (fileVO != null && SmartStringUtil.isNotEmpty(fileVO.getFileUrl())) {
            return ResponseDTO.ok(fileVO.getFileUrl());
        }

        // -------------- 2、从数据库获取文件信息 ------------------------
        if (fileVO == null) {
            fileVO = fileDao.getByFileKey(fileKey);
            if (fileVO == null) {
                return ResponseDTO.userErrorParam("文件不存在");
            }
        }

        try {
            // -------------- 3、调用亿方云API获取下载链接 ------------------------
            String accessToken = this.getAccessToken();
            if (SmartStringUtil.isEmpty(accessToken)) {
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取访问令牌失败");
            }

            HttpRequest request = HttpUtil.createGet(FANGCLOUD_API_BASE_URL + "/file/" + fileKey + "/download")
                    .header("Authorization", "Bearer " + accessToken);

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                log.error("获取亿方云文件下载链接失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取文件链接失败");
            }

            JSONObject responseJson = JSONUtil.parseObj(response.body());
            String downloadUrl = responseJson.getStr("download_url");

            if (SmartStringUtil.isEmpty(downloadUrl)) {
                log.error("获取亿方云文件下载链接失败，返回URL为空: {}", response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取文件链接失败");
            }

            // -------------- 4、缓存下载链接 ------------------------
            fileVO.setFileUrl(downloadUrl);
            redisService.set(fileRedisKey, fileVO, privateUrlExpireSeconds - 5);

            return ResponseDTO.ok(downloadUrl);

        } catch (Exception e) {
            log.error("获取亿方云文件下载链接发生异常：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取文件链接失败");
        }
    }

    @Override
    public ResponseDTO<FileDownloadVO> download(String key) {
        try {
            // -------------- 1、获取文件下载链接 ------------------------
            ResponseDTO<String> urlResponse = this.getFileUrl(key);
            if (!urlResponse.getOk()) {
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, urlResponse.getMsg());
            }

            String downloadUrl = urlResponse.getData();

            // -------------- 2、下载文件内容 ------------------------
            HttpResponse response = HttpUtil.createGet(downloadUrl).execute();

            if (!response.isOk()) {
                log.error("亿方云文件下载失败，HTTP状态码: {}", response.getStatus());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文件下载失败");
            }

            // -------------- 3、获取文件元数据 ------------------------
            FileMetadataVO metadataVO = this.getFileMetadata(key);

            // -------------- 4、构建下载结果 ------------------------
            FileDownloadVO fileDownloadVO = new FileDownloadVO();
            fileDownloadVO.setData(response.bodyBytes());
            fileDownloadVO.setMetadata(metadataVO);

            return ResponseDTO.ok(fileDownloadVO);

        } catch (Exception e) {
            log.error("亿方云文件下载发生异常：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文件下载失败");
        }
    }

    @Override
    public ResponseDTO<String> delete(String fileKey) {
        if (SmartStringUtil.isEmpty(fileKey)) {
            return ResponseDTO.userErrorParam("文件key不能为空");
        }

        try {
            // -------------- 1、调用亿方云删除文件API ------------------------
            String accessToken = this.getAccessToken();
            if (SmartStringUtil.isEmpty(accessToken)) {
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取访问令牌失败");
            }

            HttpRequest request = HttpUtil.createPost(FANGCLOUD_API_BASE_URL + "/file/" + fileKey + "/delete")
                    .header("Authorization", "Bearer " + accessToken);

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                log.error("亿方云文件删除失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文件删除失败");
            }

            JSONObject responseJson = JSONUtil.parseObj(response.body());
            if (!responseJson.getBool("success", false)) {
                log.error("亿方云文件删除失败，响应: {}", response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文件删除失败");
            }

            // -------------- 2、清除缓存 ------------------------
            String fileRedisKey = RedisKeyConst.Support.FILE_PRIVATE_VO + fileKey;
            redisService.delete(fileRedisKey);

            return ResponseDTO.ok("删除成功");

        } catch (Exception e) {
            log.error("亿方云文件删除发生异常：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文件删除失败");
        }
    }

    /**
     * 根据文件路径获取对应的文件夹ID
     *
     * @param path 文件路径
     * @return 文件夹ID
     */
    private String getFolderIdByPath(String path) {
        // 根据不同的路径映射到不同的亿方云文件夹
        // 这里可以根据业务需求进行配置
        if (path.startsWith(FileFolderTypeEnum.FOLDER_PRIVATE)) {
            return defaultFolderId; // 私有文件夹
        } else if (path.startsWith(FileFolderTypeEnum.FOLDER_PUBLIC)) {
            return defaultFolderId; // 公共文件夹
        }
        return defaultFolderId; // 默认文件夹
    }

    /**
     * 获取文件元数据信息
     *
     * @param fileKey 文件ID
     * @return 文件元数据
     */
    private FileMetadataVO getFileMetadata(String fileKey) {
        try {
            String accessToken = this.getAccessToken();
            if (SmartStringUtil.isEmpty(accessToken)) {
                return null;
            }

            HttpRequest request = HttpUtil.createGet(FANGCLOUD_API_BASE_URL + "/file/" + fileKey + "/info")
                    .header("Authorization", "Bearer " + accessToken);

            HttpResponse response = request.execute();

            if (response.isOk()) {
                JSONObject fileInfo = JSONUtil.parseObj(response.body());

                FileMetadataVO metadataVO = new FileMetadataVO();
                metadataVO.setFileName(fileInfo.getStr("name"));
                metadataVO.setFileSize(fileInfo.getLong("size"));
                metadataVO.setFileFormat(FilenameUtils.getExtension(fileInfo.getStr("name")));

                return metadataVO;
            }
        } catch (Exception e) {
            log.warn("获取亿方云文件元数据失败：", e);
        }

        return null;
    }

    /**
     * 获取访问令牌，支持自动刷新
     *
     * @return 访问令牌
     */
    private String getAccessToken() {
        try {
            // -------------- 1、优先从Redis缓存获取 ------------------------
            String accessToken = redisService.get("YFY_ACCESS_TOKEN");
            if (SmartStringUtil.isNotEmpty(accessToken)) {
                return accessToken;
            }

            // -------------- 2、检查内存缓存的令牌是否有效 ------------------------
            String token = cachedAccessToken.get();
            Long expireTime = tokenExpireTime.get();

            // 如果令牌存在且未过期（提前5分钟刷新）
            if (SmartStringUtil.isNotEmpty(token) && expireTime > System.currentTimeMillis() + 300000) {
                // 同步到Redis缓存
                long remainingSeconds = (expireTime - System.currentTimeMillis()) / 1000;
                redisService.set("YFY_ACCESS_TOKEN", token, remainingSeconds - 5);
                return token;
            }

            // -------------- 3、生成新的访问令牌 ------------------------
            synchronized (this) {
                // 双重检查，避免重复生成
                accessToken = redisService.get("YFY_ACCESS_TOKEN");
                if (SmartStringUtil.isNotEmpty(accessToken)) {
                    return accessToken;
                }

                token = cachedAccessToken.get();
                expireTime = tokenExpireTime.get();
                if (SmartStringUtil.isNotEmpty(token) && expireTime > System.currentTimeMillis() + 300000) {
                    return token;
                }

                // -------------- 4、生成JWT并获取access_token ------------------------
                String jwt = this.generateJwtToken();
                if (SmartStringUtil.isEmpty(jwt)) {
                    log.error("生成JWT失败");
                    return null;
                }

                String basicAuth = this.generateBasicAuth();

                HttpRequest request = HttpUtil.createPost(FANGCLOUD_OAUTH_URL)
                        .header("Authorization", "Basic " + basicAuth)
                        .form("grant_type", "jwt")
                        .form("assertion", jwt);

                HttpResponse response = request.execute();

                if (!response.isOk()) {
                    log.error("获取亿方云访问令牌失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                    return null;
                }

                JSONObject responseJson = JSONUtil.parseObj(response.body());
                accessToken = responseJson.getStr("access_token");
                Integer expiresIn = responseJson.getInt("expires_in");

                if (SmartStringUtil.isEmpty(accessToken)) {
                    log.error("获取亿方云访问令牌失败，响应: {}", response.body());
                    return null;
                }

                // -------------- 5、缓存令牌到内存和Redis ------------------------
                cachedAccessToken.set(accessToken);
                tokenExpireTime.set(System.currentTimeMillis() + (expiresIn * 1000L));
                redisService.set("YFY_ACCESS_TOKEN", accessToken, expiresIn - 5);

                log.info("获取亿方云访问令牌成功，有效期: {} 秒", expiresIn);
                return accessToken;
            }

        } catch (Exception e) {
            log.error("获取亿方云访问令牌发生异常：", e);
            return null;
        }
    }

    /**
     * 生成JWT令牌
     *
     * @return JWT字符串
     */
    private String generateJwtToken() {
        return FangcloudJwtUtil.generateJwtToken(enterpriseId, kid);
    }

    /**
     * 生成Basic Auth字符串
     *
     * @return Base64编码的认证字符串
     */
    private String generateBasicAuth() {
        return FangcloudJwtUtil.generateBasicAuth(clientId, clientSecret);
    }

    /**
     * 获取文件详细信息
     *
     * @param fileKey 文件ID
     * @return 文件详细信息
     */
    public ResponseDTO<JSONObject> getFileInfo(String fileKey) {
        if (SmartStringUtil.isEmpty(fileKey)) {
            return ResponseDTO.userErrorParam("文件key不能为空");
        }

        try {
            String accessToken = this.getAccessToken();
            if (SmartStringUtil.isEmpty(accessToken)) {
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取访问令牌失败");
            }

            HttpRequest request = HttpUtil.createGet(FANGCLOUD_API_BASE_URL + "/file/" + fileKey + "/info")
                    .header("Authorization", "Bearer " + accessToken);

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                log.error("获取亿方云文件信息失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取文件信息失败");
            }

            JSONObject fileInfo = JSONUtil.parseObj(response.body());
            return ResponseDTO.ok(fileInfo);

        } catch (Exception e) {
            log.error("获取亿方云文件信息发生异常：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取文件信息失败");
        }
    }

    /**
     * 复制文件到指定文件夹
     *
     * @param fileKey          文件ID
     * @param targetFolderPath 目标文件夹路径
     * @return 复制结果
     */
    public ResponseDTO<JSONObject> copyFileByPath(String fileKey, String targetFolderPath) {
        if (SmartStringUtil.isEmpty(fileKey)) {
            return ResponseDTO.userErrorParam("文件key不能为空");
        }
        if (SmartStringUtil.isEmpty(targetFolderPath)) {
            return ResponseDTO.userErrorParam("目标文件夹路径不能为空");
        }

        try {
            String accessToken = this.getAccessToken();
            if (SmartStringUtil.isEmpty(accessToken)) {
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取访问令牌失败");
            }

            Map<String, Object> params = new HashMap<>(2);
            params.put("target_folder_path", targetFolderPath);

            HttpRequest request = HttpUtil.createPost(FANGCLOUD_API_BASE_URL + "/file/" + fileKey + "/copy_by_path")
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(params));

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                log.error("亿方云复制文件失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "复制文件失败");
            }

            JSONObject result = JSONUtil.parseObj(response.body());
            return ResponseDTO.ok(result);

        } catch (Exception e) {
            log.error("亿方云复制文件发生异常：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "复制文件失败");
        }
    }

    /**
     * 移动文件到指定文件夹
     *
     * @param fileKey        文件ID
     * @param targetFolderId 目标文件夹ID
     * @return 移动结果
     */
    public ResponseDTO<String> moveFile(String fileKey, Long targetFolderId) {
        if (SmartStringUtil.isEmpty(fileKey)) {
            return ResponseDTO.userErrorParam("文件key不能为空");
        }
        if (targetFolderId == null) {
            return ResponseDTO.userErrorParam("目标文件夹ID不能为空");
        }

        try {
            String accessToken = this.getAccessToken();
            if (SmartStringUtil.isEmpty(accessToken)) {
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取访问令牌失败");
            }

            Map<String, Object> params = new HashMap<>(2);
            params.put("target_folder_id", targetFolderId);

            HttpRequest request = HttpUtil.createPost(FANGCLOUD_API_BASE_URL + "/file/" + fileKey + "/move")
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(params));

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                log.error("亿方云移动文件失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "移动文件失败");
            }

            JSONObject responseJson = JSONUtil.parseObj(response.body());
            if (!responseJson.getBool("success", false)) {
                log.error("亿方云移动文件失败，响应: {}", response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "移动文件失败");
            }

            return ResponseDTO.ok("移动成功");

        } catch (Exception e) {
            log.error("亿方云移动文件发生异常：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "移动文件失败");
        }
    }

    /**
     * 更新文件信息
     *
     * @param fileKey     文件ID
     * @param name        新文件名（可选）
     * @param description 新文件描述（可选）
     * @return 更新结果
     */
    public ResponseDTO<JSONObject> updateFileInfo(String fileKey, String name, String description) {
        if (SmartStringUtil.isEmpty(fileKey)) {
            return ResponseDTO.userErrorParam("文件key不能为空");
        }
        if (SmartStringUtil.isEmpty(name) && SmartStringUtil.isEmpty(description)) {
            return ResponseDTO.userErrorParam("文件名和描述至少要传一个");
        }

        try {
            String accessToken = this.getAccessToken();
            if (SmartStringUtil.isEmpty(accessToken)) {
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "获取访问令牌失败");
            }

            Map<String, Object> params = new HashMap<>(4);
            if (SmartStringUtil.isNotEmpty(name)) {
                params.put("name", name);
            }
            if (SmartStringUtil.isNotEmpty(description)) {
                params.put("description", description);
            }

            HttpRequest request = HttpUtil.createPost(FANGCLOUD_API_BASE_URL + "/file/" + fileKey + "/update")
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(params));

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                log.error("亿方云更新文件信息失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "更新文件信息失败");
            }

            JSONObject result = JSONUtil.parseObj(response.body());
            return ResponseDTO.ok(result);

        } catch (Exception e) {
            log.error("亿方云更新文件信息发生异常：", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "更新文件信息失败");
        }
    }
}
