package cn.edu.xmut.tsa.student.module.system.login.service;

import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import cn.edu.xmut.tsa.base.common.code.UserErrorCode;
import cn.edu.xmut.tsa.base.common.constant.RequestHeaderConst;
import cn.edu.xmut.tsa.base.common.constant.StringConst;
import cn.edu.xmut.tsa.base.common.domain.RequestUser;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.enumeration.UserTypeEnum;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.base.common.util.SmartEnumUtil;
import cn.edu.xmut.tsa.base.common.util.SmartIpUtil;
import cn.edu.xmut.tsa.base.common.util.SmartStringUtil;
import cn.edu.xmut.tsa.base.constant.LoginDeviceEnum;
import cn.edu.xmut.tsa.base.constant.RedisKeyConst;
import cn.edu.xmut.tsa.base.module.support.apiencrypt.service.ApiEncryptService;
import cn.edu.xmut.tsa.base.module.support.captcha.CaptchaService;
import cn.edu.xmut.tsa.base.module.support.captcha.domain.CaptchaVO;
import cn.edu.xmut.tsa.base.module.support.loginlog.LoginLogResultEnum;
import cn.edu.xmut.tsa.base.module.support.loginlog.LoginLogService;
import cn.edu.xmut.tsa.base.module.support.loginlog.domain.LoginLogEntity;
import cn.edu.xmut.tsa.base.module.support.loginlog.domain.LoginLogVO;
import cn.edu.xmut.tsa.base.module.support.mail.MailService;
import cn.edu.xmut.tsa.base.module.support.mail.constant.MailTemplateCodeEnum;
import cn.edu.xmut.tsa.base.module.support.redis.RedisService;
import cn.edu.xmut.tsa.base.module.support.securityprotect.domain.LoginFailEntity;
import cn.edu.xmut.tsa.base.module.support.securityprotect.service.SecurityLoginService;
import cn.edu.xmut.tsa.base.module.support.securityprotect.service.SecurityPasswordService;
import cn.edu.xmut.tsa.student.module.system.login.domain.LoginForm;
import cn.edu.xmut.tsa.student.module.system.login.domain.LoginResultVO;
import cn.edu.xmut.tsa.student.module.system.login.domain.RegisterForm;
import cn.edu.xmut.tsa.student.module.system.login.domain.RequestStudent;
import cn.edu.xmut.tsa.student.module.system.login.manager.LoginManager;
import cn.edu.xmut.tsa.student.module.system.student.dao.StudentDao;
import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import cn.edu.xmut.tsa.student.module.system.student.service.StudentService;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * 登录
 *
 * <AUTHOR> 卓大
 * @Date 2025-05-03 22:56:34
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Slf4j
@Service
public class LoginService implements StpInterface {

    /**
     * 万能密码的 sa token loginId 前缀
     */
    private static final String SUPER_PASSWORD_LOGIN_ID_PREFIX = "S";

    @Resource
    private StudentService studentService;

    @Resource
    private StudentDao studentDao;

    @Resource
    private CaptchaService captchaService;


    @Resource
    private LoginLogService loginLogService;

    @Resource
    private SecurityLoginService securityLoginService;

    @Resource
    private SecurityPasswordService protectPasswordService;

    @Resource
    private ApiEncryptService apiEncryptService;

    @Resource
    private SecurityPasswordService securityPasswordService;

    @Resource
    private MailService mailService;

    @Resource
    private RedisService redisService;

    @Resource
    private LoginManager loginManager;

    /**
     * 获取验证码
     */
    public ResponseDTO<CaptchaVO> getCaptcha() {
        return ResponseDTO.ok(captchaService.generateCaptcha());
    }

    /**
     * 考生登陆
     *
     * @return 返回用户登录信息
     */
    public ResponseDTO<LoginResultVO> login(LoginForm loginForm, String ip, String userAgent) {

        LoginDeviceEnum loginDeviceEnum = SmartEnumUtil.getEnumByValue(loginForm.getLoginDevice(), LoginDeviceEnum.class);
        if (loginDeviceEnum == null) {
            return ResponseDTO.userErrorParam("登录设备暂不支持！");
        }

        // 验证登录名
        StudentEntity studentEntity = studentService.getByLoginName(loginForm.getLoginName());
        if (null == studentEntity) {
            return ResponseDTO.userErrorParam("登录名或密码错误！");
        }

        // 验证账号状态
        if (studentEntity.getDeletedFlag()) {
            saveLoginLog(studentEntity, ip, userAgent, "账号已删除", LoginLogResultEnum.LOGIN_FAIL, loginDeviceEnum);
            return ResponseDTO.userErrorParam("您的账号已被删除,请联系工作人员！");
        }

        // 解密前端加密的密码
        String requestPassword = apiEncryptService.decrypt(loginForm.getPassword());

        // 按照等保登录要求，进行登录失败次数校验
        ResponseDTO<LoginFailEntity> loginFailEntityResponseDTO = securityLoginService.checkLogin(studentEntity.getStudentId(), UserTypeEnum.STUDENT);
        if (!loginFailEntityResponseDTO.getOk()) {
            return ResponseDTO.error(loginFailEntityResponseDTO);
        }

        // 密码错误
        if (!SecurityPasswordService.matchesPwd(requestPassword, studentEntity.getLoginPwd())) {
            // 记录登录失败
            saveLoginLog(studentEntity, ip, userAgent, "密码错误", LoginLogResultEnum.LOGIN_FAIL, loginDeviceEnum);
            // 记录等级保护次数
            String msg = securityLoginService.recordLoginFail(studentEntity.getStudentId(), UserTypeEnum.STUDENT, studentEntity.getLoginName(), loginFailEntityResponseDTO.getData());
            return msg == null ? ResponseDTO.userErrorParam("登录名或密码错误！") : ResponseDTO.error(UserErrorCode.LOGIN_FAIL_WILL_LOCK, msg);
        }

        String saTokenLoginId = UserTypeEnum.STUDENT.getValue() + StringConst.COLON + studentEntity.getStudentId();

        // 登录
        StpUtil.login(saTokenLoginId, String.valueOf(loginDeviceEnum.getDesc()));

        // 获取员工信息
        RequestStudent requestStudent = loginManager.loadLoginInfo(studentEntity);

        // 移除登录失败
        securityLoginService.removeLoginFail(studentEntity.getStudentId(), UserTypeEnum.STUDENT);

        // 获取登录结果信息
        String token = StpUtil.getTokenValue();
        LoginResultVO loginResultVO = getLoginResult(requestStudent, token);

        //保存登录记录
        saveLoginLog(studentEntity, ip, userAgent, StringConst.EMPTY, LoginLogResultEnum.LOGIN_SUCCESS, loginDeviceEnum);

        // 设置 token
        loginResultVO.setToken(token);

        return ResponseDTO.ok(loginResultVO);
    }


    /**
     * 获取登录结果信息
     */
    public LoginResultVO getLoginResult(RequestStudent requestStudent, String token) {

        // 基础信息
        LoginResultVO loginResultVO = SmartBeanUtil.copy(requestStudent, LoginResultVO.class);

        // 上次登录信息
        LoginLogVO loginLogVO = loginLogService.queryLastByUserId(requestStudent.getStudentId(), UserTypeEnum.STUDENT, LoginLogResultEnum.LOGIN_SUCCESS);
        if (loginLogVO != null) {
            loginResultVO.setLastLoginIp(loginLogVO.getLoginIp());
            loginResultVO.setLastLoginIpRegion(loginLogVO.getLoginIpRegion());
            loginResultVO.setLastLoginTime(loginLogVO.getCreateTime());
            loginResultVO.setLastLoginUserAgent(loginLogVO.getUserAgent());
        }

        // 是否需要强制修改密码
        boolean needChangePasswordFlag = protectPasswordService.checkNeedChangePassword(requestStudent.getUserType().getValue(), requestStudent.getUserId());
        loginResultVO.setNeedUpdatePwdFlag(needChangePasswordFlag);

        // 万能密码登录，则不需要设置强制修改密码
        String loginIdByToken = (String) StpUtil.getLoginIdByToken(token);
        if (loginIdByToken != null && loginIdByToken.startsWith(SUPER_PASSWORD_LOGIN_ID_PREFIX)) {
            loginResultVO.setNeedUpdatePwdFlag(false);
        }

        return loginResultVO;
    }


    public RequestStudent getLoginStudent(String loginId, HttpServletRequest request) {
        if (loginId == null) {
            return null;
        }

        Long requestStudentId = getStudentIdByLoginId(loginId);
        if (requestStudentId == null) {
            return null;
        }

        RequestStudent requestStudent = loginManager.getRequestStudent(requestStudentId);

        // 更新请求ip和user agent
        requestStudent.setUserAgent(JakartaServletUtil.getHeaderIgnoreCase(request, RequestHeaderConst.USER_AGENT));
        requestStudent.setIp(JakartaServletUtil.getClientIP(request));

        return requestStudent;
    }

    /**
     * 根据 loginId 获取 员工id
     */
    Long getStudentIdByLoginId(String loginId) {

        if (loginId == null) {
            return null;
        }

        try {
            String studentIdStr = loginId.substring(2);

            return Long.parseLong(studentIdStr);
        } catch (Exception e) {
            log.error("loginId parse error , loginId : {}", loginId, e);
            return null;
        }
    }


    /**
     * 退出登录
     */
    public ResponseDTO<String> logout(RequestUser requestUser) {

        // sa token 登出
        StpUtil.logout();

        // 清空登录信息缓存
        loginManager.clear();

        //保存登出日志
        LoginLogEntity loginEntity = LoginLogEntity.builder()
                .userId(requestUser.getUserId())
                .userType(requestUser.getUserType().getValue())
                .userName(requestUser.getUserName())
                .userAgent(requestUser.getUserAgent())
                .loginIp(requestUser.getIp())
                .loginIpRegion(SmartIpUtil.getRegion(requestUser.getIp()))
                .loginResult(LoginLogResultEnum.LOGIN_OUT.getValue())
                .createTime(LocalDateTime.now())
                .build();
        loginLogService.log(loginEntity);

        return ResponseDTO.ok();
    }

    /**
     * 保存登录日志
     */
    private void saveLoginLog(StudentEntity studentEntity, String ip, String userAgent, String remark, LoginLogResultEnum result, LoginDeviceEnum loginDeviceEnum) {
        LoginLogEntity loginEntity = LoginLogEntity.builder()
                .userId(studentEntity.getStudentId())
                .userType(UserTypeEnum.STUDENT.getValue())
                .userName(studentEntity.getActualName())
                .userAgent(userAgent)
                .loginIp(ip)
                .loginIpRegion(SmartIpUtil.getRegion(ip))
                .remark(remark)
                .loginDevice(loginDeviceEnum.getDesc())
                .loginResult(result.getValue())
                .createTime(LocalDateTime.now())
                .build();
        loginLogService.log(loginEntity);
    }


    /**
     * 发送邮箱验证码
     *
     * @param email
     * @return 发送结果
     */
    public ResponseDTO<String> sendEmailCode(String email) {
        if (StrUtil.isBlank(email)) {
            return ResponseDTO.userErrorParam("请输入邮箱");
        }

        StudentEntity existStudent = studentDao.queryByEmail(email);

        if (existStudent != null && !existStudent.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("该邮箱已注册");
        }

        // 校验验证码发送时间，60秒内不能重复发生
        String redisVerificationCodeKey = redisService.generateRedisKey(RedisKeyConst.Support.REGISTER_VERIFICATION_CODE, UserTypeEnum.STUDENT.getValue() + RedisKeyConst.SEPARATOR + email);
        String emailCode = redisService.get(redisVerificationCodeKey);
        long sendCodeTimeMills = -1;
        if (!SmartStringUtil.isEmpty(emailCode)) {
            sendCodeTimeMills = NumberUtil.parseLong(emailCode.split(StringConst.UNDERLINE)[1]);
        }

        if (System.currentTimeMillis() - sendCodeTimeMills < 60 * 1000) {
            return ResponseDTO.userErrorParam("邮箱验证码已发送，一分钟内请勿重复发送");
        }

        //生成验证码
        long currentTimeMillis = System.currentTimeMillis();
        String verificationCode = RandomUtil.randomNumbers(6);
        redisService.set(redisVerificationCodeKey, verificationCode + StringConst.UNDERLINE + currentTimeMillis, 300);

        // 发送邮件验证码
        HashMap<String, Object> mailParams = new HashMap<>();
        mailParams.put("code", verificationCode);
        return mailService.sendMail(MailTemplateCodeEnum.REGISTER_VERIFICATION_CODE, mailParams, Collections.singletonList(email));
    }

    /**
     * 校验邮箱验证码
     */
    private ResponseDTO<String> validateEmailCode(RegisterForm registerForm) {
        if (SmartStringUtil.isEmpty(registerForm.getEmailCode())) {
            return ResponseDTO.userErrorParam("请输入邮箱验证码");
        }

        // 校验验证码
        String redisVerificationCodeKey = redisService.generateRedisKey(RedisKeyConst.Support.REGISTER_VERIFICATION_CODE, UserTypeEnum.STUDENT.getValue() + RedisKeyConst.SEPARATOR + registerForm.getEmail());
        String emailCode = redisService.get(redisVerificationCodeKey);
        if (SmartStringUtil.isEmpty(emailCode)) {
            return ResponseDTO.userErrorParam("邮箱验证码已失效，请重新发送");
        }

        if (!emailCode.split(StringConst.UNDERLINE)[0].equals(registerForm.getEmailCode().trim())) {
            return ResponseDTO.userErrorParam("邮箱验证码错误，请重新填写");
        }

        return ResponseDTO.ok();
    }

    /**
     * 移除邮箱验证码
     */
    private void deleteEmailCode(String email) {
        String redisVerificationCodeKey = redisService.generateRedisKey(RedisKeyConst.Support.REGISTER_VERIFICATION_CODE, UserTypeEnum.STUDENT.getValue() + RedisKeyConst.SEPARATOR + email);
        redisService.delete(redisVerificationCodeKey);
    }

    /**
     * 学生注册
     *
     * @param registerForm 注册表单
     * @return 注册结果
     */
    public ResponseDTO<String> register(RegisterForm registerForm) {
        // 验证密码一致性
        if (!registerForm.getPassword().equals(registerForm.getConfirmPassword())) {
            return ResponseDTO.userErrorParam("两次输入的密码不一致");
        }

        // 检查邮箱是否已注册
        StudentEntity existStudent = studentDao.queryByEmail(registerForm.getEmail());
        if (existStudent != null && !existStudent.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("该邮箱已注册");
        }

        // 验证邮箱验证码
        ResponseDTO<String> validateEmailCode = validateEmailCode(registerForm);
        if (!validateEmailCode.getOk()) {
            return ResponseDTO.error(validateEmailCode);
        }

        // 解密密码
        String decryptedPassword = apiEncryptService.decrypt(registerForm.getPassword());

        // 校验密码复杂度
        ResponseDTO<String> validatePassComplexity = securityPasswordService.validatePasswordComplexity(decryptedPassword);
        if (!validatePassComplexity.getOk()) {
            return validatePassComplexity;
        }

        // 创建学生实体
        StudentEntity studentEntity = new StudentEntity();
        studentEntity.setLoginName(registerForm.getEmail());
        studentEntity.setLoginPwd(SecurityPasswordService.getEncryptPwd(decryptedPassword));
        studentEntity.setActualName(registerForm.getActualName());
        studentEntity.setPhone(registerForm.getPhone());
        studentEntity.setEmail(registerForm.getEmail());
        studentEntity.setDeletedFlag(false);
        studentEntity.setCreateTime(LocalDateTime.now());
        studentEntity.setUpdateTime(LocalDateTime.now());

        // 保存学生信息
        try {
            studentDao.insert(studentEntity);
            // 删除验证码
            deleteEmailCode(registerForm.getEmail());
            return ResponseDTO.ok("注册成功");
        } catch (Exception e) {
            log.error("注册失败", e);
            return ResponseDTO.userErrorParam("注册失败，请稍后重试");
        }
    }

    @Override
    public List<String> getPermissionList(Object o, String s) {
        return List.of();
    }

    @Override
    public List<String> getRoleList(Object o, String s) {
        return List.of();
    }
}
