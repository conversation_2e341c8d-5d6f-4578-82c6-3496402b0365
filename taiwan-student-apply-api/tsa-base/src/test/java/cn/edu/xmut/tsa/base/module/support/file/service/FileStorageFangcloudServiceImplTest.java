package cn.edu.xmut.tsa.base.module.support.file.service;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.module.support.file.domain.vo.FileUploadVO;
import cn.edu.xmut.tsa.base.module.support.file.util.FangcloudJwtUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 亿方云文件存储服务测试（JWT模式）
 *
 * <AUTHOR>
 * @Date 2025-06-21
 */
@SpringBootTest
@ActiveProfiles("test")
class FileStorageFangcloudServiceImplTest {

    /**
     * 测试文件上传 - 文件名为空的情况
     */
    @Test
    void testUploadWithEmptyFileName() {
        // 创建文件存储服务实例
        FileStorageFangcloudServiceImpl service = new FileStorageFangcloudServiceImpl();
        
        // 创建一个文件名为空的MockMultipartFile
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            null, // 文件名为空
            "text/plain", 
            "test content".getBytes()
        );
        
        // 执行上传
        ResponseDTO<FileUploadVO> result = service.upload(file, "test/");
        
        // 验证结果
        assertFalse(result.getOk());
        assertEquals("上传文件名为空", result.getMsg());
    }

    /**
     * 测试获取文件URL - fileKey为空的情况
     */
    @Test
    void testGetFileUrlWithEmptyKey() {
        FileStorageFangcloudServiceImpl service = new FileStorageFangcloudServiceImpl();
        
        // 测试空字符串
        ResponseDTO<String> result1 = service.getFileUrl("");
        assertFalse(result1.getOk());
        assertEquals("文件不存在，key为空", result1.getMsg());
        
        // 测试null
        ResponseDTO<String> result2 = service.getFileUrl(null);
        assertFalse(result2.getOk());
        assertEquals("文件不存在，key为空", result2.getMsg());
    }

    /**
     * 测试删除文件 - fileKey为空的情况
     */
    @Test
    void testDeleteWithEmptyKey() {
        FileStorageFangcloudServiceImpl service = new FileStorageFangcloudServiceImpl();
        
        // 测试空字符串
        ResponseDTO<String> result1 = service.delete("");
        assertFalse(result1.getOk());
        assertEquals("文件key不能为空", result1.getMsg());
        
        // 测试null
        ResponseDTO<String> result2 = service.delete(null);
        assertFalse(result2.getOk());
        assertEquals("文件key不能为空", result2.getMsg());
    }

    /**
     * 测试JWT工具类 - Basic Auth生成
     */
    @Test
    void testGenerateBasicAuth() {
        String basicAuth = FangcloudJwtUtil.generateBasicAuth("testClientId", "testClientSecret");
        assertNotNull(basicAuth);
        assertTrue(basicAuth.length() > 0);
        
        // 验证Base64编码的格式
        try {
            java.util.Base64.getDecoder().decode(basicAuth);
        } catch (Exception e) {
            fail("Basic Auth应该是有效的Base64编码");
        }
    }

    /**
     * 测试JWT格式验证
     */
    @Test
    void testJwtFormatValidation() {
        // 测试无效的JWT格式
        assertFalse(FangcloudJwtUtil.isValidJwtFormat(null));
        assertFalse(FangcloudJwtUtil.isValidJwtFormat(""));
        assertFalse(FangcloudJwtUtil.isValidJwtFormat("invalid.jwt"));
        assertFalse(FangcloudJwtUtil.isValidJwtFormat("invalid.jwt.format.extra"));
    }

    /**
     * 测试JWT过期时间解析
     */
    @Test
    void testJwtExpireTimeParsingWithInvalidJwt() {
        // 测试无效JWT的过期时间解析
        assertNull(FangcloudJwtUtil.getJwtExpireTime(null));
        assertNull(FangcloudJwtUtil.getJwtExpireTime(""));
        assertNull(FangcloudJwtUtil.getJwtExpireTime("invalid.jwt"));
    }

    /**
     * 测试JWT即将过期判断
     */
    @Test
    void testJwtExpiringSoonWithInvalidJwt() {
        // 无效JWT应该被认为是即将过期的
        assertTrue(FangcloudJwtUtil.isJwtExpiringSoon(null));
        assertTrue(FangcloudJwtUtil.isJwtExpiringSoon(""));
        assertTrue(FangcloudJwtUtil.isJwtExpiringSoon("invalid.jwt"));
    }

    /**
     * 测试根据路径获取文件夹ID的逻辑
     */
    @Test
    void testGetFolderIdByPath() {
        FileStorageFangcloudServiceImpl service = new FileStorageFangcloudServiceImpl();
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = service.getClass()
                .getDeclaredMethod("getFolderIdByPath", String.class);
            method.setAccessible(true);
            
            // 测试不同路径
            String result1 = (String) method.invoke(service, "private/");
            String result2 = (String) method.invoke(service, "public/");
            String result3 = (String) method.invoke(service, "other/");
            
            // 由于默认都返回defaultFolderId，这里主要是验证方法不会抛异常
            assertNotNull(result1);
            assertNotNull(result2);
            assertNotNull(result3);
            
        } catch (Exception e) {
            fail("测试getFolderIdByPath方法时出现异常: " + e.getMessage());
        }
    }
}
