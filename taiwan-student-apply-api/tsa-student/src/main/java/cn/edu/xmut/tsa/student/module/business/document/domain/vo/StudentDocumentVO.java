package cn.edu.xmut.tsa.student.module.business.document.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学生资料VO
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Schema(description = "学生资料VO")
public class StudentDocumentVO {

    @Schema(description = "资料ID")
    private Long documentId;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "资料类型")
    private Integer documentType;

    @Schema(description = "资料类型名称")
    private String documentTypeName;

    @Schema(description = "是否必需")
    private Boolean required;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件访问URL")
    private String fileUrl;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
