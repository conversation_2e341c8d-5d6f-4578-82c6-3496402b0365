package cn.edu.xmut.tsa.student.module.business.major.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 专业志愿表单
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Schema(description = "专业志愿表单")
public class StudentMajorChoiceForm {

    @Schema(description = "科类")
    @NotNull(message = "科类不能为空")
    private Integer subjectType;

    @Schema(description = "第一专业志愿ID")
    @NotNull(message = "第一专业志愿不能为空")
    private Long firstMajorId;

    @Schema(description = "第二专业志愿ID")
    @NotNull(message = "第二专业志愿不能为空")
    private Long secondMajorId;

    @Schema(description = "第三专业志愿ID")
    @NotNull(message = "第三专业志愿不能为空")
    private Long thirdMajorId;
}
