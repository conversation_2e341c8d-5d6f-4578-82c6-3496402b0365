package cn.edu.xmut.tsa.student.module.business.major.dao;

import cn.edu.xmut.tsa.student.module.business.major.domain.entity.StudentMajorChoiceEntity;
import cn.edu.xmut.tsa.student.module.business.major.domain.vo.StudentMajorChoiceVO;
import cn.edu.xmut.tsa.student.module.business.major.domain.vo.EnrollmentPlanStudentVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 专业志愿DAO
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Mapper
public interface StudentMajorChoiceDao extends BaseMapper<StudentMajorChoiceEntity> {

    /**
     * 根据学生ID查询专业志愿
     *
     * @param studentId 学生ID
     * @return 专业志愿
     */
    StudentMajorChoiceVO getByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据科类查询当年招生计划
     *
     * @param subjectType 科类
     * @param year 年度
     * @return 招生计划列表
     */
    List<EnrollmentPlanStudentVO> queryEnrollmentPlanBySubjectType(@Param("subjectType") Integer subjectType, @Param("year") Integer year);

    /**
     * 检查学生是否已选择专业志愿
     *
     * @param studentId 学生ID
     * @return 数量
     */
    Long countByStudentId(@Param("studentId") Long studentId);
}
