package cn.edu.xmut.tsa.student.module.business.major.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 招生计划VO（学生端）
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Schema(description = "招生计划VO（学生端）")
public class EnrollmentPlanStudentVO {

    @Schema(description = "招生计划ID")
    private Long enrollmentPlanId;

    @Schema(description = "招生年度")
    private Integer year;

    @Schema(description = "科类")
    private Integer subjectType;

    @Schema(description = "科类名称")
    private String subjectTypeName;

    @Schema(description = "专业名称")
    private String majorName;

    @Schema(description = "所属学院")
    private String collegeName;

    @Schema(description = "学制（年）")
    private Integer educationDuration;

    @Schema(description = "备注")
    private String remark;
}
