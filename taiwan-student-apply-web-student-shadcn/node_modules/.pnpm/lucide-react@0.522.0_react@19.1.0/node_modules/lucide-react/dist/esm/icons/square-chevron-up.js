/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", key: "afitv7" }],
  ["path", { d: "m8 14 4-4 4 4", key: "fy2ptz" }]
];
const SquareChevronUp = createLucideIcon("square-chevron-up", __iconNode);

export { __iconNode, SquareChevronUp as default };
//# sourceMappingURL=square-chevron-up.js.map
