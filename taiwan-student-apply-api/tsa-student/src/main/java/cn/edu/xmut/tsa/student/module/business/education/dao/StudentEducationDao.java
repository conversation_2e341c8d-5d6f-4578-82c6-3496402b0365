package cn.edu.xmut.tsa.student.module.business.education.dao;

import cn.edu.xmut.tsa.student.module.business.education.domain.entity.StudentEducationEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教育情况DAO
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Mapper
public interface StudentEducationDao extends BaseMapper<StudentEducationEntity> {

    /**
     * 根据学生ID查询教育情况列表
     *
     * @param studentId 学生ID
     * @return 教育情况列表
     */
    List<StudentEducationEntity> queryByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据学生ID统计教育情况数量
     *
     * @param studentId 学生ID
     * @return 数量
     */
    Long countByStudentId(@Param("studentId") Long studentId);
}
