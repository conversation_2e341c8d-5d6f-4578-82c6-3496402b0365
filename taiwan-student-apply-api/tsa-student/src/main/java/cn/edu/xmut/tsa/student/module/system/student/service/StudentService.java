package cn.edu.xmut.tsa.student.module.system.student.service;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.enumeration.StudentStatusEnum;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.student.module.system.student.dao.StudentDao;
import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEvent;
import cn.edu.xmut.tsa.student.module.system.student.domain.event.StudentStatusChangeEventType;
import cn.edu.xmut.tsa.student.module.system.student.domain.form.StudentInfoForm;
import cn.edu.xmut.tsa.student.module.system.student.domain.vo.StudentInfoVO;
import cn.edu.xmut.tsa.student.module.system.student.manager.StudentStatusManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 学生信息服务
 *
 * <AUTHOR>
 * @Date 2025-06-20 11:00:00
 */
@Slf4j
@Service
public class StudentService {

    @Resource
    private StudentDao studentDao;

    @Resource
    private StudentStatusManager studentStatusManager;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 根据学生ID获取学生信息
     *
     * @param studentId 学生ID
     * @return 学生信息
     */
    public ResponseDTO<StudentInfoVO> getStudentInfo(Long studentId) {
        StudentEntity studentEntity = studentDao.selectById(studentId);
        if (studentEntity == null || studentEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("学生信息不存在");
        }

        StudentInfoVO studentInfoVO = SmartBeanUtil.copy(studentEntity, StudentInfoVO.class);
        return ResponseDTO.ok(studentInfoVO);
    }

    public StudentEntity getById(Long studentId) {
        return studentDao.selectById(studentId);
    }


    public StudentEntity getByLoginName(String loginName) {
        return studentDao.getByLoginName(loginName, false);
    }

    /**
     * 更新学生个人信息
     *
     * @param studentId 学生ID
     * @param form 个人信息表单
     * @return 更新结果
     */
    public ResponseDTO<String> updateStudentInfo(Long studentId, StudentInfoForm form) {
        // 检查学生是否存在
        StudentEntity studentEntity = studentDao.selectById(studentId);
        if (studentEntity == null || studentEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("学生信息不存在");
        }

        // 检查邮箱是否被其他学生使用（移除邮箱修改功能）
        // 邮箱地址不可修改，只从原实体获取
        form.setEmail(studentEntity.getEmail());

        // 更新学生信息
        studentEntity.setAvatar(form.getAvatar());
        studentEntity.setGender(form.getGender());
        studentEntity.setPhone(form.getPhone());
        studentEntity.setTaiwanPassportNumber(form.getTaiwanPassportNumber());
        studentEntity.setTaiwanIdCardNumber(form.getTaiwanIdCardNumber());
        studentEntity.setBirthPlace(form.getBirthPlace());
        studentEntity.setBirthDate(form.getBirthDate());
        studentEntity.setMailingAddress(form.getMailingAddress());
        studentEntity.setContactPhone(form.getContactPhone());
        studentEntity.setEmergencyContactName(form.getEmergencyContactName());
        studentEntity.setEmergencyContactPhone(form.getEmergencyContactPhone());
        studentEntity.setPersonalStatement(form.getPersonalStatement());
        studentEntity.setUpdateTime(LocalDateTime.now());

        try {
            studentDao.updateById(studentEntity);
            
            // 发布学生状态变更事件
            publishStatusChangeEvent(studentId, StudentStatusChangeEventType.PERSONAL_INFO_UPDATED);
            
            return ResponseDTO.ok("个人信息更新成功");
        } catch (Exception e) {
            log.error("更新学生个人信息失败", e);
            return ResponseDTO.userErrorParam("更新失败，请稍后重试");
        }
    }

    /**
     * 发布学生状态变更事件
     *
     * @param studentId 学生ID
     * @param eventType 事件类型
     */
    public void publishStatusChangeEvent(Long studentId, StudentStatusChangeEventType eventType) {
        try {
            StudentStatusChangeEvent event = new StudentStatusChangeEvent(this, studentId, eventType);
            eventPublisher.publishEvent(event);
            log.debug("发布学生状态变更事件成功, studentId: {}, eventType: {}", studentId, eventType.getDesc());
        } catch (Exception e) {
            log.error("发布学生状态变更事件失败, studentId: {}, eventType: {}", studentId, eventType.getDesc(), e);
        }
    }

    /**
     * 检查学生是否有教育情况
     *
     * @param studentId 学生ID
     * @return 是否有教育情况
     */
    public boolean hasEducation(Long studentId) {
        return studentStatusManager.hasEducationInfo(studentId);
    }

    /**
     * 检查学生是否已选择专业志愿
     *
     * @param studentId 学生ID
     * @return 是否已选择
     */
    public boolean hasMajorChoice(Long studentId) {
        return studentStatusManager.hasMajorChoiceInfo(studentId);
    }

    /**
     * 检查学生是否已上传所有必需资料
     *
     * @param studentId 学生ID
     * @return 是否已上传所有必需资料
     */
    public boolean hasAllRequiredDocuments(Long studentId) {
        return studentStatusManager.hasAllRequiredDocuments(studentId);
    }
}
