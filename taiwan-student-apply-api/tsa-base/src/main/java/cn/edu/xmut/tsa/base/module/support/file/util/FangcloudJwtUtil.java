package cn.edu.xmut.tsa.base.module.support.file.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;

/**
 * 亿方云JWT工具类
 *
 * <AUTHOR>
 * @Date 2025-06-21
 */
@Slf4j
public class FangcloudJwtUtil {

    /**
     * JWT过期时间（秒）
     */
    private static final int JWT_EXPIRE_SECONDS = 60;

    private static final String PRIVATE_KEY_PEM = "-----BEGIN PRIVATE KEY-----\n" +
            "        MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCwyktdaoNeuO9e\n" +
            "        wYlJniVrnKxr3Zgs1egaWNUA65KSl8rSJ0BlgQzJ5+U9xIx/oDhuzvamB9oyXeVI\n" +
            "        fNHenc9yR0ISxiIFPSg/PkDANGjKI9qcV7Ii76gcynHnmFuR+M640HRpVYpPBpvu\n" +
            "        GvsEbltbYOP0mHqPnrbSKd+BgAYCfHqrOubzy3n5Zho2QHpdXLWEYp/P0IJ08OJS\n" +
            "        amgANq3pKUzEXzNK56k7UE/Lwm+KYz/LkUtHvsfOfnkiFJROjX2PUIRx2x0Jqn0G\n" +
            "        e8qElC2AFA1+Q1kfV/bSLMTLkPoo9ztEH9HpuunhNsX8cWN87ezPGUinJL1oslqo\n" +
            "        hswq1m/vAgMBAAECggEBAKbOeMWvljxtqg1x9qNrgEtFDLrJ3qePPOx/HJs9qpJ+\n" +
            "        fnRDbNNZLEPldbsOFLLm7dg/lyGZDxuu1IPMogi2ReHBsmocYEUpKyxhBJ9V0/nG\n" +
            "        UqJpbD7ieXCQ42BttRjJ9eMTbINmnpVp2v6Snn3T0ocpqGxE4n9YdY9eyvBZZ1I3\n" +
            "        p6EJir1yVlBbaLXX4ywqke/Jpt8Xo9q4R4fWcYVfQeZJ/yQIRQ2XxXPGaDoYryu5\n" +
            "        hOEOxVWuTQkgSZ+eNX/9YWFXL/C5bYVMBTsMncyFCUnH/7MyIKd8YQ6qgHLRZ3Br\n" +
            "        QOOsLgL51QuAyQyZMlh0uAjL9AE+r2vFu3NlJvSjskECgYEA3GVhIs9LaqQptGii\n" +
            "        VGGEZ9Z+NWI46P+a+RznA/PJvmOWPmfl/brvSBEyqLVbHwXOOixocfaia3uvg7W5\n" +
            "        BzTEFnoi5+9gN4hPDW6x9Ld8DJuDMxAkkrmdTgFNRw8V24tOVrmXI/eMwmwsHRW5\n" +
            "        3h8X0mNxjSQP0tGAeasZqFXgickCgYEAzVmRlRVcKLENujQ4e5Tza35zIoFWeT1Q\n" +
            "        EpNEdRsk4unLqyio7jKrDHIedtAviRGBGQ1qw5raikvUyv9C8ECkFmQ3ZJqNc23g\n" +
            "        VeETNW4YQOQ6s8yWSeSj4yTZqm0Rge9OPP4Ae85YYcJaNh5dkmA1Uh8yszoj/+0a\n" +
            "        XfJP5uaFB/cCgYEA1X0SocCBQlSw3UP58pjfxCH+8UEq9XAbszFvCeINy3H2KI9G\n" +
            "        NwjsHjgihU/fa2aZRLqdlb8dx9XwxzmxeKyBQXCf/WFgJ4rPYKiYkv9ll2TQqxb5\n" +
            "        BnPsT5JtQ+b1+kuv38w4/qsd23sjJC1WdTZTQgX4CYskFMfBlpnJ6cj+DxkCgYBl\n" +
            "        RQbGvwH0D1z1t0sjpxFksbeWDLLYl6QlhPr9xaUrQqFuh17IGbTBLoZXkdPfhQJl\n" +
            "        mHhOfs/H5B3M/pXEZhyF7mZi9tVLV6SKtbUDZIgReHx6rypuxpMMMeAmbCN2MM4k\n" +
            "        xh7274wmqfJTPiZnSYsCNOCMAuA+FnAqZJWDCawRgQKBgQCCbQgwb2K/4De/MU9w\n" +
            "        tU1nDWaQ7PGl5RzxGXrDrPJ6Cp4l5APP3IDYRxNkRk84Q6Wv6LVFFxMsuRpOGsHo\n" +
            "        ZRc6k/npPQne6feAmYOqeYiElM5AxrjsUSdb3gINw0ZZkmYULGTX+ZjriA6yhN7T\n" +
            "        AziC5ocsUjpV6ZaiQSqWpyGpQA==\n" +
            "        -----END PRIVATE KEY-----";

    /**
     * 生成亿方云JWT令牌
     *
     * @param enterpriseId 企业ID
     * @param kid          公钥唯一标识
     * @return JWT字符串
     */
    public static String generateJwtToken(String enterpriseId, String kid) {

        try {
            // -------------- 1、解析私钥 ------------------------
            PrivateKey privateKey = parsePrivateKey();
            if (privateKey == null) {
                log.error("解析私钥失败");
                return null;
            }

            // -------------- 2、构建JWT Header ------------------------
            Map<String, Object> header = new HashMap<>(4);
            header.put("alg", "RS256");
            header.put("typ", "JWT");
            header.put("kid", kid);

            // -------------- 3、构建JWT Claims ------------------------
            Date currentTime = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentTime);
            calendar.add(Calendar.SECOND, JWT_EXPIRE_SECONDS);
            Date expireTime = calendar.getTime();

            String jwtId = generateJwtId();

            // -------------- 4、生成JWT ------------------------
            String jwt = Jwts.builder()
                    .setHeader(header)
                    .claim("yifangyun_sub_type", "user")
                    .setSubject("901097")
                    .setExpiration(expireTime)
                    .setIssuedAt(currentTime)
                    .setId(jwtId)
                    .signWith(SignatureAlgorithm.RS256, privateKey)
                    .compact();

            log.debug("生成JWT成功，过期时间: {}", expireTime);
            return jwt;

        } catch (Exception e) {
            log.error("生成JWT令牌发生异常：", e);
            return null;
        }
    }

    /**
     * 解析私钥字符串为PrivateKey对象
     *
     * @return PrivateKey对象
     */
    public static PrivateKey parsePrivateKey() {
        try {
            // -------------- 1、清理PEM格式 ------------------------
            String privateKeyContent = PRIVATE_KEY_PEM
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                    .replace("-----END RSA PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");

            // -------------- 2、Base64解码 ------------------------
            byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);

            // -------------- 3、生成PrivateKey对象 ------------------------
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");

            return keyFactory.generatePrivate(keySpec);

        } catch (Exception e) {
            log.error("解析私钥发生异常：", e);
            return null;
        }
    }

    /**
     * 生成Basic Auth字符串
     *
     * @param clientId     客户端ID
     * @param clientSecret 客户端密钥
     * @return Base64编码的认证字符串
     */
    public static String generateBasicAuth(String clientId, String clientSecret) {
        String credentials = clientId + ":" + clientSecret;
        return Base64.getEncoder().encodeToString(credentials.getBytes());
    }

    /**
     * 验证JWT格式是否正确（仅验证格式，不验证签名）
     *
     * @param jwt JWT字符串
     * @return 是否格式正确
     */
    public static boolean isValidJwtFormat(String jwt) {
        if (jwt == null || jwt.trim().isEmpty()) {
            return false;
        }

        String[] parts = jwt.split("\\.");
        if (parts.length != 3) {
            return false;
        }

        try {
            // 验证header
            String headerJson = new String(Base64.getUrlDecoder().decode(parts[0]));
            JSONObject header = JSONUtil.parseObj(headerJson);
            if (!"RS256".equals(header.getStr("alg"))) {
                return false;
            }

            // 验证payload
            String payloadJson = new String(Base64.getUrlDecoder().decode(parts[1]));
            JSONObject payload = JSONUtil.parseObj(payloadJson);
            if (!"user".equals(payload.getStr("yifangyun_sub_type"))) {
                return false;
            }

            return true;
        } catch (Exception e) {
            log.warn("JWT格式验证失败：", e);
            return false;
        }
    }

    /**
     * 解析JWT中的过期时间
     *
     * @param jwt JWT字符串
     * @return 过期时间戳（秒），解析失败返回null
     */
    public static Long getJwtExpireTime(String jwt) {
        try {
            String[] parts = jwt.split("\\.");
            if (parts.length != 3) {
                return null;
            }

            String payloadJson = new String(Base64.getUrlDecoder().decode(parts[1]));
            JSONObject payload = JSONUtil.parseObj(payloadJson);

            return payload.getLong("exp");
        } catch (Exception e) {
            log.warn("解析JWT过期时间失败：", e);
            return null;
        }
    }

    /**
     * 检查JWT是否即将过期（提前5秒判断）
     *
     * @param jwt JWT字符串
     * @return 是否即将过期
     */
    public static boolean isJwtExpiringSoon(String jwt) {
        Long expireTime = getJwtExpireTime(jwt);
        if (expireTime == null) {
            return true;
        }

        long currentTimeSeconds = System.currentTimeMillis() / 1000;
        return expireTime <= currentTimeSeconds + 5;
    }

    /**
     * 生成JWT ID
     *
     * @return JWT ID字符串
     */
    private static String generateJwtId() {
        SecureRandom rand = new SecureRandom();
        byte[] csrf = new byte[16];
        rand.nextBytes(csrf);
        return Base64.getEncoder().encodeToString(csrf);
    }
}
