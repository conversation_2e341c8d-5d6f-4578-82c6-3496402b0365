# 亿方云文件存储集成说明（JWT 模式）

## 概述

本项目已集成亿方云文件存储服务，采用 JWT 认证模式，支持文件的上传、下载、删除等操作。JWT 模式是亿方云提供的企业级认证方式，安全性更高，适合服务端到服务端的调用。

## 功能特点

- ✅ JWT 认证：使用企业私钥签名生成 JWT，安全性更高
- ✅ 自动刷新：访问令牌自动获取和刷新，无需手动管理
- ✅ 文件上传：支持多种格式文件上传到亿方云
- ✅ 文件下载：支持通过文件 ID 下载文件
- ✅ 文件删除：支持删除云端文件
- ✅ 文件信息获取：获取文件元数据信息
- ✅ 缓存优化：支持 Redis 缓存文件链接，提高访问性能
- ✅ 私有文件支持：支持私有文件的临时访问链接

## 配置说明

### 1. 应用配置

在 `tsa-base.yaml` 中配置亿方云相关参数：

```yaml
file:
  storage:
    mode: fangcloud # 设置为亿方云模式
    fangcloud:
      client-id: your_client_id # 亿方云应用的Client ID
      client-secret: your_client_secret # 亿方云应用的Client Secret
      enterprise-id: your_enterprise_id # 企业ID（数字）
      private-key: | # 企业私钥（PEM格式）
        -----BEGIN PRIVATE KEY-----
        # 这里放置你的RSA私钥内容
        -----END PRIVATE KEY-----
      kid: your_kid # 公钥唯一标识
      folder-id: 0 # 默认上传文件夹ID，0表示根目录
      private-url-expire-seconds: 3600 # 私有文件链接过期时间（秒）
```

### 2. 获取亿方云配置信息

#### 2.1 注册企业账号

1. 访问 [亿方云官网](https://www.fangcloud.com/)
2. 注册企业账号并完成认证

#### 2.2 创建应用

1. 登录企业控制台
2. 进入"开发者中心" > "应用管理"
3. 创建新应用，获取 `client_id` 和 `client_secret`

#### 2.3 生成 RSA 密钥对

使用 OpenSSL 工具生成 RSA 密钥对：

```bash
# 生成私钥
openssl genrsa -out private_key.pem 2048

# 生成公钥
openssl rsa -in private_key.pem -pubout -out public_key.pem
```

#### 2.4 提交公钥获取 KID

1. 将 `public_key.pem` 文件内容提交给亿方云客服
2. 亿方云会返回一个 `kid` 作为该公钥的唯一标识

#### 2.5 联系客服开通 JWT 模式

JWT 模式需要联系亿方云客服开通，提供企业信息和使用场景说明。

## JWT 认证流程

### 1. JWT Token 生成

系统会自动生成 JWT Token，包含以下步骤：

1. **构建 JWT Header**:

   ```json
   {
     "alg": "RS256",
     "kid": "your_kid"
   }
   ```

2. **构建 JWT Claims**:

   ```json
   {
     "yifangyun_sub_type": "enterprise",
     "sub": "your_enterprise_id",
     "exp": 1640995200,
     "iat": 1640995140,
     "jti": "unique_jwt_id"
   }
   ```

3. **使用私钥签名**:
   使用 RS256 算法和企业私钥对 JWT 进行签名

### 2. Access Token 获取

系统会自动通过 JWT 换取 access_token：

1. **请求地址**: `https://oauth.fangcloud.com/oauth/token`
2. **认证方式**: Basic Auth (client_id:client_secret)
3. **请求参数**:
   - `grant_type`: jwt
   - `assertion`: JWT 字符串

### 3. 令牌缓存和刷新

- 系统会自动缓存 access_token
- 在令牌过期前 5 分钟自动刷新
- 支持多线程安全的令牌管理

## 使用方法

### 1. 启用亿方云存储

在配置文件中设置：

```yaml
file:
  storage:
    mode: fangcloud
```

### 2. 文件上传

```java
@Autowired
private IFileStorageService fileStorageService;

// 上传文件
ResponseDTO<FileUploadVO> result = fileStorageService.upload(multipartFile, "path/to/folder/");
```

### 3. 文件下载

```java
// 下载文件
ResponseDTO<FileDownloadVO> result = fileStorageService.download(fileKey);
```

### 4. 获取文件链接

```java
// 获取文件访问链接
ResponseDTO<String> result = fileStorageService.getFileUrl(fileKey);
```

### 5. 删除文件

```java
// 删除文件
ResponseDTO<String> result = fileStorageService.delete(fileKey);
```

## API 接口说明

### JWT 认证流程

- **OAuth 接口**: `POST https://oauth.fangcloud.com/oauth/token`
- **认证方式**: Basic Auth + JWT
- **请求参数**:
  - `grant_type`: jwt
  - `assertion`: JWT 字符串

### 文件上传

- **接口地址**: `POST /api/v2/file/upload`
- **认证方式**: Bearer Token
- **请求方式**: `multipart/form-data`
- **主要参数**:
  - `folder_id`: 上传到的文件夹 ID
  - `name`: 文件名称
  - `size`: 文件大小
  - `file`: 文件内容

### 文件下载

- **接口地址**: `GET /api/v2/file/download`
- **认证方式**: Bearer Token
- **请求方式**: `GET`
- **主要参数**:
  - `file_id`: 文件 ID

### 文件删除

- **接口地址**: `POST /api/v2/file/delete`
- **认证方式**: Bearer Token
- **请求方式**: `POST`
- **主要参数**:
  - `file_id`: 文件 ID

### 文件信息

- **接口地址**: `GET /api/v2/file/info`
- **认证方式**: Bearer Token
- **请求方式**: `GET`
- **主要参数**:
  - `file_id`: 文件 ID

## 注意事项

1. **JWT 模式要求**:

   - 需要联系亿方云客服开通 JWT 模式
   - 需要提供企业 RSA 公钥获取 kid
   - 企业 ID 必须是数字格式

2. **私钥安全**:

   - 私钥必须妥善保管，不能泄露
   - 建议使用环境变量或配置中心管理私钥
   - 私钥格式必须是 PKCS#8 PEM 格式

3. **JWT 过期时间**:

   - JWT 有效期为 60 秒，不能超过 iat+60s
   - 系统会自动处理 JWT 的生成和刷新

4. **访问权限**:

   - 公共文件：直接返回下载链接
   - 私有文件：生成临时访问链接，有过期时间

5. **缓存策略**:

   - Access Token 会缓存，自动刷新
   - 私有文件的访问链接会缓存到 Redis 中
   - 缓存时间为 `private-url-expire-seconds - 5` 秒

6. **错误处理**:
   - 所有 API 调用都包含完整的错误处理
   - JWT 生成失败会记录详细日志
   - 令牌刷新失败会自动重试

## 故障排查

### 常见问题

1. **JWT 生成失败**

   - 检查私钥格式是否正确（PKCS#8 PEM）
   - 检查企业 ID 是否为数字格式
   - 检查 kid 是否正确

2. **令牌获取失败**

   - 检查 client_id 和 client_secret 是否正确
   - 检查 JWT 是否在有效期内
   - 检查是否开通了 JWT 模式

3. **文件操作失败**

   - 检查令牌是否有效
   - 检查文件 ID 是否存在
   - 检查网络连接是否正常

4. **配置问题**
   - 确认所有配置参数正确
   - 确认私钥格式正确
   - 确认企业 ID 为数字

### 日志查看

相关日志会输出到应用日志文件中，搜索关键字：

- `亿方云文件上传`
- `亿方云文件下载`
- `亿方云文件删除`
- `生成JWT`
- `获取亿方云访问令牌`

### RSA 密钥格式说明

私钥必须是 PKCS#8 格式，如果是 PKCS#1 格式需要转换：

```bash
# PKCS#1转PKCS#8
openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in pkcs1_key.pem -out pkcs8_key.pem
```

## 联系支持

如有问题，请联系：

- 亿方云客服热线：400-993-9050
- 开发文档：https://open.fangcloud.com/doc/api
- JWT 模式开通：需联系亿方云客服
