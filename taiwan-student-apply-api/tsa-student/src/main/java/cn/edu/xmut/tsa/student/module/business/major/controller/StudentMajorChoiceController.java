package cn.edu.xmut.tsa.student.module.business.major.controller;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartRequestUtil;
import cn.edu.xmut.tsa.student.module.business.major.domain.form.StudentMajorChoiceForm;
import cn.edu.xmut.tsa.student.module.business.major.domain.vo.EnrollmentPlanStudentVO;
import cn.edu.xmut.tsa.student.module.business.major.domain.vo.StudentMajorChoiceVO;
import cn.edu.xmut.tsa.student.module.business.major.service.StudentMajorChoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 专业志愿控制器
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Slf4j
@Tag(name = "专业志愿管理")
@RestController
public class StudentMajorChoiceController {

    @Resource
    private StudentMajorChoiceService studentMajorChoiceService;

    /**
     * 查询学生专业志愿
     */
    @GetMapping("/student/major-choice")
    @Operation(summary = "查询学生专业志愿  <AUTHOR>
    public ResponseDTO<StudentMajorChoiceVO> getMajorChoice() {
        return studentMajorChoiceService.getByStudentId(SmartRequestUtil.getRequestUserId());
    }

    /**
     * 保存专业志愿
     */
    @PostMapping("/student/major-choice/save")
    @Operation(summary = "保存专业志愿  <AUTHOR>
    public ResponseDTO<String> saveMajorChoice(@Valid @RequestBody StudentMajorChoiceForm form) {
        return studentMajorChoiceService.saveMajorChoice(SmartRequestUtil.getRequestUserId(), form);
    }

    /**
     * 根据科类查询当年招生计划
     */
    @GetMapping("/student/enrollment-plan/query/{subjectType}")
    @Operation(summary = "根据科类查询当年招生计划  <AUTHOR>
    public ResponseDTO<List<EnrollmentPlanStudentVO>> queryEnrollmentPlanBySubjectType(@PathVariable Integer subjectType) {
        return studentMajorChoiceService.queryEnrollmentPlanBySubjectType(subjectType);
    }
}
