<!--
  * 主应用页面
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2022-09-12 23:46:47
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->

<template>
  <div class="app-container">
    <!---全局loading--->
    <div v-if="spinning" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p class="loading-text">稍等片刻，我在拼命加载中...</p>
      </div>
    </div>
    <!--- 路由 -->
    <RouterView />
    <!--- Toast 容器 -->
    <Toaster />
  </div>
</template>

<script setup>
  import { computed } from 'vue';
  import { useSpinStore } from '/@/store/modules/system/spin';
  import Toaster from '/@/components/ui/toaster.vue';

  // 全局loading
  let spinStore = useSpinStore();
  const spinning = computed(() => spinStore.loading);
</script>
<style scoped>
  .app-container {
    width: 100%;
    height: 100vh;
    position: relative;
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid hsl(var(--border));
    border-top: 4px solid hsl(var(--primary));
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .loading-text {
    color: hsl(var(--muted-foreground));
    font-size: 14px;
    margin: 0;
  }
</style>
