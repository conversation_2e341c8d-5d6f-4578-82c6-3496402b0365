package cn.edu.xmut.tsa.student.module.business.major.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 专业志愿实体类
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_student_major_choice")
public class StudentMajorChoiceEntity {

    /**
     * 专业志愿ID
     */
    @TableId(type = IdType.AUTO)
    private Long choiceId;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 科类
     */
    private Integer subjectType;

    /**
     * 第一专业志愿ID
     */
    private Long firstMajorId;

    /**
     * 第二专业志愿ID
     */
    private Long secondMajorId;

    /**
     * 第三专业志愿ID
     */
    private Long thirdMajorId;

    /**
     * 是否删除：0-否，1-是
     */
    private Boolean deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
