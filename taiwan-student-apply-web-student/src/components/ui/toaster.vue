<script setup>
import { toast } from '@/lib/toast'
import Toast from './toast.vue'
</script>

<template>
  <div class="fixed top-0 right-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]">
    <Toast
      v-for="toastItem in toast.toasts.value"
      :key="toastItem.id"
      :type="toastItem.type"
      :title="toastItem.title"
      :description="toastItem.description"
      :duration="0"
      @close="toast.removeToast(toastItem.id)"
    />
  </div>
</template>
