package cn.edu.xmut.tsa.student.module.business.major.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 专业志愿VO
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Data
@Schema(description = "专业志愿VO")
public class StudentMajorChoiceVO {

    @Schema(description = "专业志愿ID")
    private Long choiceId;

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "科类")
    private Integer subjectType;

    @Schema(description = "科类名称")
    private String subjectTypeName;

    @Schema(description = "第一专业志愿ID")
    private Long firstMajorId;

    @Schema(description = "第一专业志愿名称")
    private String firstMajorName;

    @Schema(description = "第一专业所属学院")
    private String firstCollegeName;

    @Schema(description = "第二专业志愿ID")
    private Long secondMajorId;

    @Schema(description = "第二专业志愿名称")
    private String secondMajorName;

    @Schema(description = "第二专业所属学院")
    private String secondCollegeName;

    @Schema(description = "第三专业志愿ID")
    private Long thirdMajorId;

    @Schema(description = "第三专业志愿名称")
    private String thirdMajorName;

    @Schema(description = "第三专业所属学院")
    private String thirdCollegeName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
