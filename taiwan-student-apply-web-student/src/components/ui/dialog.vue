<script setup>
  import { cn } from '/@/lib/utils.js';
  import Button from './button.vue';

  const props = defineProps({
    open: Boolean,
    title: String,
    width: String,
    confirmLoading: Boolean,
    class: String,
  });

  const emit = defineEmits(['ok', 'cancel', 'update:open']);

  const handleOk = () => {
    emit('ok');
  };

  const handleCancel = () => {
    emit('cancel');
    emit('update:open', false);
  };

  const handleOverlayClick = (event) => {
    if (event.target === event.currentTarget) {
      handleCancel();
    }
  };
</script>

<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-200 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="open" class="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4" @click="handleOverlayClick">
        <Transition
          enter-active-class="transition-all duration-200 ease-out"
          enter-from-class="opacity-0 scale-95"
          enter-to-class="opacity-100 scale-100"
          leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 scale-100"
          leave-to-class="opacity-0 scale-95"
        >
          <div
            v-if="open"
            :class="cn('bg-background rounded-lg shadow-lg max-h-[90vh] overflow-auto', props.class)"
            :style="{ width: width || '500px' }"
            @click.stop
          >
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b">
              <h2 class="text-lg font-semibold">{{ title }}</h2>
              <button
                @click="handleCancel"
                class="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <!-- Content -->
            <div class="p-6">
              <slot />
            </div>

            <!-- Footer -->
            <div class="flex items-center justify-end space-x-2 p-6 border-t">
              <Button variant="outline" @click="handleCancel"> 取消 </Button>
              <Button @click="handleOk" :disabled="confirmLoading">
                <div v-if="confirmLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                确定
              </Button>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>
