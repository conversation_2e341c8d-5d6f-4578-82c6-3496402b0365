package cn.edu.xmut.tsa.student.module.business.document.dao;

import cn.edu.xmut.tsa.student.module.business.document.domain.entity.StudentDocumentEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生资料DAO
 *
 * <AUTHOR>
 * @Date 2025-06-21 11:00:00
 */
@Mapper
public interface StudentDocumentDao extends BaseMapper<StudentDocumentEntity> {

    /**
     * 根据学生ID查询资料列表
     *
     * @param studentId 学生ID
     * @return 资料列表
     */
    List<StudentDocumentEntity> queryByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据学生ID和资料类型查询资料
     *
     * @param studentId 学生ID
     * @param documentType 资料类型
     * @return 资料
     */
    StudentDocumentEntity getByStudentIdAndType(@Param("studentId") Long studentId, @Param("documentType") Integer documentType);

    /**
     * 根据学生ID统计已上传的必需资料数量
     *
     * @param studentId 学生ID
     * @return 已上传的必需资料数量
     */
    Long countRequiredDocumentsByStudentId(@Param("studentId") Long studentId);
}
